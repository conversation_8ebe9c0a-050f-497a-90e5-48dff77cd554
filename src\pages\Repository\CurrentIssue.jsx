import { useState } from 'react';
import Navbar from '../../Components/NavbarClean';
import Footer from '../../Components/Footer';

function CurrentIssue() {
  const [selectedArticle, setSelectedArticle] = useState(null);

  const currentIssue = {
    volume: "Volume 15",
    issue: "Issue 2",
    date: "December 2024",
    coverImage: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    description: "This issue explores contemporary challenges in legal theory, constitutional law, and international jurisprudence."
  };

  const articles = [
    {
      id: 1,
      title: "Constitutional Interpretation in the Digital Age: Privacy Rights and Technological Surveillance",
      authors: ["<PERSON>. <PERSON>", "Prof<PERSON> <PERSON>"],
      pages: "1-28",
      doi: "10.1234/jls.2024.15.2.001",
      abstract: "This article examines the evolving landscape of constitutional privacy rights in response to advancing surveillance technologies...",
      keywords: ["Constitutional Law", "Privacy Rights", "Digital Surveillance", "Fourth Amendment"],
      type: "Research Article"
    },
    {
      id: 2,
      title: "International Trade Law and Environmental Protection: Reconciling Economic and Ecological Interests",
      authors: ["Prof. Maria Rodriguez", "Dr. David Thompson"],
      pages: "29-56",
      doi: "10.1234/jls.2024.15.2.002",
      abstract: "This study analyzes the tension between international trade agreements and environmental protection measures...",
      keywords: ["International Trade", "Environmental Law", "WTO", "Sustainable Development"],
      type: "Research Article"
    },
    {
      id: 3,
      title: "Artificial Intelligence and Legal Decision-Making: Ethical Considerations for Judicial Systems",
      authors: ["Dr. Robert Kim", "Prof. Lisa Anderson"],
      pages: "57-82",
      doi: "10.1234/jls.2024.15.2.003",
      abstract: "As AI systems become increasingly integrated into legal processes, this article explores the ethical implications...",
      keywords: ["Artificial Intelligence", "Judicial Ethics", "Legal Technology", "Due Process"],
      type: "Research Article"
    },
    {
      id: 4,
      title: "Corporate Social Responsibility and Legal Accountability: A Comparative Analysis",
      authors: ["Prof. Emma Wilson"],
      pages: "83-104",
      doi: "10.1234/jls.2024.15.2.004",
      abstract: "This comparative study examines how different legal systems approach corporate social responsibility...",
      keywords: ["Corporate Law", "Social Responsibility", "Comparative Law", "Business Ethics"],
      type: "Research Article"
    },
    {
      id: 5,
      title: "Recent Developments in Human Rights Law: A Global Perspective",
      authors: ["Dr. Ahmed Hassan", "Prof. Catherine Brown"],
      pages: "105-118",
      doi: "10.1234/jls.2024.15.2.005",
      abstract: "This review article surveys recent developments in international human rights law...",
      keywords: ["Human Rights", "International Law", "Global Governance", "Legal Development"],
      type: "Review Article"
    }
  ];

  const handleDownload = (articleId, format) => {
    // Simulate download functionality
    console.log(`Downloading article ${articleId} in ${format} format`);
    alert(`Downloading article in ${format} format...`);
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        {/* Header Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Current Issue
              </h1>
              <p className="text-xl" style={{color: '#6b7c5d'}}>
                Latest Research in Legal Studies
              </p>
            </div>
          </div>
        </div>

        {/* Issue Information and Cover */}
        <div className="py-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Cover Image */}
              <div className="lg:col-span-1">
                <div className="sticky top-8">
                  <div className="rounded-lg shadow-lg overflow-hidden" style={{backgroundColor: '#faf9f7'}}>
                    <img 
                      src={currentIssue.coverImage} 
                      alt="Journal Cover"
                      className="w-full h-96 object-cover"
                    />
                    <div className="p-6">
                      <h2 className="text-2xl font-bold mb-2" style={{color: '#4a5d3a'}}>
                        {currentIssue.volume}
                      </h2>
                      <h3 className="text-xl font-semibold mb-2" style={{color: '#6b7c5d'}}>
                        {currentIssue.issue}
                      </h3>
                      <p className="text-sm mb-4" style={{color: '#6b7c5d'}}>
                        Published: {currentIssue.date}
                      </p>
                      <p className="text-sm mb-6" style={{color: '#6b7c5d'}}>
                        {currentIssue.description}
                      </p>
                      <div className="space-y-3">
                        <button 
                          className="w-full py-2 px-4 rounded-lg font-semibold transition-colors"
                          style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                          onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                          onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                          onClick={() => handleDownload('full-issue', 'PDF')}
                        >
                          Download Full Issue (PDF)
                        </button>
                        <button 
                          className="w-full py-2 px-4 rounded-lg font-semibold transition-colors border"
                          style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#6b7c5d';
                            e.target.style.color = '#f5f5f0';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7c5d';
                          }}
                        >
                          View Table of Contents
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Articles List */}
              <div className="lg:col-span-2">
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold mb-8" style={{color: '#4a5d3a'}}>
                    Articles in This Issue
                  </h2>
                  
                  {articles.map((article, index) => (
                    <div 
                      key={article.id} 
                      className="rounded-lg shadow-md p-6 transition-shadow hover:shadow-lg"
                      style={{backgroundColor: '#faf9f7'}}
                    >
                      <div className="flex justify-between items-start mb-4">
                        <span 
                          className="px-3 py-1 rounded-full text-xs font-semibold"
                          style={{backgroundColor: '#e8ede6', color: '#4a5d3a'}}
                        >
                          {article.type}
                        </span>
                        <span className="text-sm" style={{color: '#6b7c5d'}}>
                          Pages {article.pages}
                        </span>
                      </div>
                      
                      <h3 className="text-xl font-bold mb-3" style={{color: '#4a5d3a'}}>
                        {article.title}
                      </h3>
                      
                      <div className="mb-3">
                        <p className="text-sm" style={{color: '#6b7c5d'}}>
                          <strong>Authors: <AUTHORS>
                        </p>
                        <p className="text-sm" style={{color: '#6b7c5d'}}>
                          <strong>DOI:</strong> {article.doi}
                        </p>
                      </div>
                      
                      <p className="text-sm mb-4" style={{color: '#6b7c5d'}}>
                        {article.abstract}
                      </p>
                      
                      <div className="mb-4">
                        <p className="text-sm" style={{color: '#6b7c5d'}}>
                          <strong>Keywords:</strong> {article.keywords.join(', ')}
                        </p>
                      </div>
                      
                      <div className="flex flex-wrap gap-3">
                        <button 
                          className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                          style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                          onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                          onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                          onClick={() => handleDownload(article.id, 'PDF')}
                        >
                          Download PDF
                        </button>
                        <button 
                          className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors border"
                          style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#6b7c5d';
                            e.target.style.color = '#f5f5f0';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7c5d';
                          }}
                        >
                          View Abstract
                        </button>
                        <button 
                          className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors border"
                          style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#6b7c5d';
                            e.target.style.color = '#f5f5f0';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7c5d';
                          }}
                        >
                          Cite Article
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#e8ede6'}}>
                  <span className="text-2xl" style={{color: '#4a5d3a'}}>📖</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Open Access
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  All articles are freely available for download and distribution under our open access policy.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#e8ede6'}}>
                  <span className="text-2xl" style={{color: '#4a5d3a'}}>🔍</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Peer Reviewed
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Every article undergoes rigorous double-blind peer review to ensure academic excellence.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: '#e8ede6'}}>
                  <span className="text-2xl" style={{color: '#4a5d3a'}}>📊</span>
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Indexed
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Our journal is indexed in major academic databases for maximum visibility and impact.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default CurrentIssue;
