import React from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Box,
  Chip,
  Paper,
  Divider,
  IconButton,
  ThemeProvider,
  createTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Email, LinkedIn, School, Work } from '@mui/icons-material';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

// Custom theme with professional colors
const theme = createTheme({
  palette: {
    primary: {
      main: '#1565c0', // Professional blue
      light: '#42a5f5',
      dark: '#0d47a1',
    },
    secondary: {
      main: '#f57c00', // Accent orange
      light: '#ffb74d',
      dark: '#e65100',
    },
    background: {
      default: '#f8fafc',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
  },
});

// Styled Components
const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
  border: '1px solid rgba(0,0,0,0.05)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
  },
}));

const GradientBox = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: '20px',
  padding: theme.spacing(6),
  color: 'white',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
    borderRadius: '20px',
  },
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 120,
  height: 120,
  border: '4px solid white',
  boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
  },
}));

const editorialData = {
  editorInChief: {
    name: "Dr. Anjali Sharma",
    position: "Professor, Department of English",
    institution: "XYZ College, University of ABC",
    email: "<EMAIL>",
    linkedin: "https://linkedin.com/in/anjali-sharma",
    specialization: ["Literary Criticism", "Modern Literature", "Academic Writing"],
    experience: "15+ years",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
    bio: "Dr. Sharma is a distinguished scholar in English Literature with extensive experience in academic publishing and peer review processes.",
  },
  associateEditors: [
    {
      name: "Dr. Rajeev Kumar",
      position: "Associate Professor, Dept. of Physics",
      institution: "XYZ College",
      email: "<EMAIL>",
      linkedin: "https://linkedin.com/in/rajeev-kumar",
      specialization: ["Quantum Physics", "Research Methodology"],
      experience: "12+ years",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
      bio: "Renowned physicist with expertise in quantum mechanics and scientific publication standards.",
    },
    {
      name: "Dr. Sneha Mehta",
      position: "Assistant Professor, Dept. of History",
      institution: "XYZ College",
      email: "<EMAIL>",
      linkedin: "https://linkedin.com/in/sneha-mehta",
      specialization: ["Ancient History", "Archaeological Studies"],
      experience: "8+ years",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
      bio: "Expert historian specializing in ancient civilizations and archaeological research methodologies.",
    },
  ],
  advisoryBoard: [
    {
      name: "Prof. A.K. Verma",
      institution: "IIT Delhi",
      position: "Professor Emeritus",
      specialization: ["Engineering", "Technology Innovation"],
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
    },
    {
      name: "Dr. Neha Reddy",
      institution: "University of Mumbai",
      position: "Dean, Research Affairs",
      specialization: ["Research Ethics", "Academic Policy"],
      image: "https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400&h=400&fit=crop&crop=face",
    },
    {
      name: "Prof. Michael Chen",
      institution: "Stanford University",
      position: "Distinguished Professor",
      specialization: ["International Relations", "Policy Studies"],
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?w=400&h=400&fit=crop&crop=face",
    },
    {
      name: "Dr. Sarah Williams",
      institution: "Oxford University",
      position: "Research Director",
      specialization: ["Digital Humanities", "Academic Innovation"],
      image: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=400&fit=crop&crop=face",
    },
  ],
};

const EditorialBoard = () => {
  return (
    <>
      <Navbar />
      <ThemeProvider theme={theme}>
        <Box sx={{ backgroundColor: 'background.default', minHeight: '100vh' }}>
          {/* Hero Section */}
          <GradientBox sx={{ mb: 8, mx: { xs: 2, md: 4 }, mt: 4 }}>
            <Container maxWidth="lg">
              <Box textAlign="center" sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', md: '3.5rem' },
                    mb: 3,
                    fontWeight: 700,
                  }}
                >
                  Editorial Board
                </Typography>
                <Typography
                  variant="h5"
                  sx={{
                    opacity: 0.95,
                    maxWidth: '800px',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontSize: { xs: '1.1rem', md: '1.3rem' },
                  }}
                >
                  Our journal is guided by a distinguished team of scholars and professionals
                  committed to maintaining the highest standards of academic excellence and peer review.
                </Typography>
              </Box>
            </Container>
          </GradientBox>

          <Container maxWidth="lg" sx={{ pb: 8 }}>
            {/* Editor-in-Chief Section */}
            <Box sx={{ mb: 8 }}>
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  mb: 4,
                  textAlign: 'center',
                  color: 'primary.main',
                }}
              >
                Editor-in-Chief
              </Typography>

              <StyledCard sx={{ p: 4, maxWidth: '800px', mx: 'auto' }}>
                <CardContent sx={{ p: 0 }}>
                  <Grid container spacing={4} alignItems="center">
                    <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
                      <ProfileAvatar
                        src={editorialData.editorInChief.image}
                        alt={editorialData.editorInChief.name}
                        sx={{ mx: 'auto', mb: 2 }}
                      />
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                        <IconButton color="primary" size="small">
                          <Email />
                        </IconButton>
                        <IconButton color="primary" size="small">
                          <LinkedIn />
                        </IconButton>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={8}>
                      <Typography variant="h4" sx={{ mb: 1, color: 'primary.main', fontWeight: 600 }}>
                        {editorialData.editorInChief.name}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Work sx={{ mr: 1, color: 'text.secondary', fontSize: '1.2rem' }} />
                        <Typography variant="h6" color="text.secondary">
                          {editorialData.editorInChief.position}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <School sx={{ mr: 1, color: 'text.secondary', fontSize: '1.2rem' }} />
                        <Typography variant="body1" color="text.secondary">
                          {editorialData.editorInChief.institution}
                        </Typography>
                      </Box>

                      <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.6 }}>
                        {editorialData.editorInChief.bio}
                      </Typography>

                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                        {editorialData.editorInChief.specialization.map((spec, index) => (
                          <Chip
                            key={index}
                            label={spec}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>

                      <Chip
                        label={`Experience: ${editorialData.editorInChief.experience}`}
                        color="secondary"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </StyledCard>
            </Box>

            {/* Associate Editors Section */}
            <Box sx={{ mb: 8 }}>
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  mb: 4,
                  textAlign: 'center',
                  color: 'primary.main',
                }}
              >
                Associate Editors
              </Typography>

              <Grid container spacing={4}>
                {editorialData.associateEditors.map((editor, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <StyledCard sx={{ height: '100%' }}>
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Avatar
                            src={editor.image}
                            alt={editor.name}
                            sx={{
                              width: 80,
                              height: 80,
                              mr: 3,
                              border: '3px solid',
                              borderColor: 'primary.light',
                            }}
                          />
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="h5" sx={{ mb: 1, color: 'primary.main', fontWeight: 600 }}>
                              {editor.name}
                            </Typography>
                            <Box sx={{ display: 'flex', justifyContent: 'flex-start', gap: 1 }}>
                              <IconButton color="primary" size="small">
                                <Email />
                              </IconButton>
                              <IconButton color="primary" size="small">
                                <LinkedIn />
                              </IconButton>
                            </Box>
                          </Box>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Work sx={{ mr: 1, color: 'text.secondary', fontSize: '1rem' }} />
                          <Typography variant="body1" color="text.secondary">
                            {editor.position}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <School sx={{ mr: 1, color: 'text.secondary', fontSize: '1rem' }} />
                          <Typography variant="body2" color="text.secondary">
                            {editor.institution}
                          </Typography>
                        </Box>

                        <Typography variant="body2" sx={{ mb: 2, lineHeight: 1.5 }}>
                          {editor.bio}
                        </Typography>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          {editor.specialization.map((spec, specIndex) => (
                            <Chip
                              key={specIndex}
                              label={spec}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                        </Box>

                        <Chip
                          label={`Experience: ${editor.experience}`}
                          color="secondary"
                          size="small"
                        />
                      </CardContent>
                    </StyledCard>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Advisory Board Section */}
            <Box sx={{ mb: 8 }}>
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2rem', md: '2.5rem' },
                  mb: 4,
                  textAlign: 'center',
                  color: 'primary.main',
                }}
              >
                Advisory Board
              </Typography>

              <Grid container spacing={3}>
                {editorialData.advisoryBoard.map((advisor, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <StyledCard sx={{ height: '100%', textAlign: 'center' }}>
                      <CardContent sx={{ p: 3 }}>
                        <Avatar
                          src={advisor.image}
                          alt={advisor.name}
                          sx={{
                            width: 100,
                            height: 100,
                            mx: 'auto',
                            mb: 2,
                            border: '3px solid',
                            borderColor: 'primary.light',
                          }}
                        />

                        <Typography variant="h6" sx={{ mb: 1, color: 'primary.main', fontWeight: 600 }}>
                          {advisor.name}
                        </Typography>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {advisor.position}
                        </Typography>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {advisor.institution}
                        </Typography>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                          {advisor.specialization.map((spec, specIndex) => (
                            <Chip
                              key={specIndex}
                              label={spec}
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          ))}
                        </Box>
                      </CardContent>
                    </StyledCard>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Call to Action Section */}
            <Paper
              elevation={0}
              sx={{
                background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                borderRadius: '20px',
                p: 6,
                textAlign: 'center',
              }}
            >
              <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 600 }}>
                Join Our Editorial Team
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, maxWidth: '600px', mx: 'auto', lineHeight: 1.6 }}>
                We are always looking for distinguished scholars and professionals to join our editorial board.
                If you are interested in contributing to academic excellence, we would love to hear from you.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Chip
                  label="Contact Us"
                  color="primary"
                  clickable
                  sx={{ px: 2, py: 1, fontSize: '1rem', fontWeight: 600 }}
                />
                <Chip
                  label="Learn More"
                  variant="outlined"
                  color="primary"
                  clickable
                  sx={{ px: 2, py: 1, fontSize: '1rem', fontWeight: 600 }}
                />
              </Box>
            </Paper>
          </Container>
        </Box>
      </ThemeProvider>
      <Footer />
    </>
  );
};

export default EditorialBoard;
