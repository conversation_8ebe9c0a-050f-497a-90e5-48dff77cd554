import { useState } from 'react';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

function EditorialBoardNew() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const boardMembers = [
    {
      id: 1,
      name: "Prof. Dr. <PERSON>",
      position: "Editor-in-Chief",
      affiliation: "Harvard Law School, USA",
      specialization: "Constitutional Law, Human Rights",
      email: "m.<PERSON><PERSON><PERSON>@harvard.edu",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      category: "chief",
      bio: "Prof<PERSON> is a distinguished scholar in constitutional law with over 20 years of experience in legal academia and practice.",
      publications: 85,
      hIndex: 42
    },
    {
      id: 2,
      name: "Dr. <PERSON>",
      position: "Associate Editor",
      affiliation: "Stanford Law School, USA",
      specialization: "International Law, Trade Law",
      email: "<EMAIL>",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      category: "associate",
      bio: "Dr. <PERSON> specializes in international trade law and has served as a consultant for various international organizations.",
      publications: 62,
      hIndex: 35
    },
    {
      id: 3,
      name: "Prof. James Wilson",
      position: "Managing Editor",
      affiliation: "Oxford University, UK",
      specialization: "Criminal Law, Legal Theory",
      email: "<EMAIL>",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      category: "managing",
      bio: "Prof. Wilson is renowned for his work in criminal law theory and has authored several influential textbooks.",
      publications: 78,
      hIndex: 38
    },
    {
      id: 4,
      name: "Dr. Maria Rodriguez",
      position: "Section Editor - International Law",
      affiliation: "Universidad Complutense Madrid, Spain",
      specialization: "International Human Rights, European Law",
      email: "<EMAIL>",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      category: "section",
      bio: "Dr. Rodriguez is an expert in international human rights law with extensive experience in European legal systems.",
      publications: 54,
      hIndex: 29
    },
    {
      id: 5,
      name: "Prof. David Kim",
      position: "Section Editor - Corporate Law",
      affiliation: "Seoul National University, South Korea",
      specialization: "Corporate Law, Securities Regulation",
      email: "<EMAIL>",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      category: "section",
      bio: "Prof. Kim is a leading authority on corporate governance and securities regulation in Asia.",
      publications: 67,
      hIndex: 33
    },
    {
      id: 6,
      name: "Dr. Emma Thompson",
      position: "Book Review Editor",
      affiliation: "University of Cambridge, UK",
      specialization: "Legal History, Jurisprudence",
      email: "<EMAIL>",
      image: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
      category: "review",
      bio: "Dr. Thompson specializes in legal history and jurisprudence with a focus on common law development.",
      publications: 43,
      hIndex: 26
    }
  ];

  const categories = [
    { id: 'all', name: 'All Members', count: boardMembers.length },
    { id: 'chief', name: 'Editor-in-Chief', count: boardMembers.filter(m => m.category === 'chief').length },
    { id: 'associate', name: 'Associate Editors', count: boardMembers.filter(m => m.category === 'associate').length },
    { id: 'managing', name: 'Managing Editors', count: boardMembers.filter(m => m.category === 'managing').length },
    { id: 'section', name: 'Section Editors', count: boardMembers.filter(m => m.category === 'section').length },
    { id: 'review', name: 'Review Editors', count: boardMembers.filter(m => m.category === 'review').length }
  ];

  const filteredMembers = selectedCategory === 'all' 
    ? boardMembers 
    : boardMembers.filter(member => member.category === selectedCategory);

  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        
        {/* Header Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Editorial Board
              </h1>
              <p className="text-xl max-w-3xl mx-auto" style={{color: '#6b7c5d'}}>
                Our distinguished editorial board comprises leading experts in legal studies from prestigious institutions worldwide, ensuring the highest standards of academic excellence and scholarly integrity.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="py-12">
          <div className="max-w-7xl mx-auto px-4">
            
            {/* Category Filter */}
            <div className="mb-8">
              <div className="flex flex-wrap justify-center gap-4">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-6 py-3 rounded-lg font-semibold transition-all ${
                      selectedCategory === category.id 
                        ? 'shadow-lg transform scale-105' 
                        : 'hover:shadow-md'
                    }`}
                    style={{
                      backgroundColor: selectedCategory === category.id ? '#6b7c5d' : '#faf9f7',
                      color: selectedCategory === category.id ? '#f5f5f0' : '#6b7c5d',
                      border: selectedCategory === category.id ? 'none' : '2px solid #e8ede6'
                    }}
                  >
                    {category.name} ({category.count})
                  </button>
                ))}
              </div>
            </div>

            {/* Board Members Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredMembers.map((member) => (
                <div 
                  key={member.id}
                  className="rounded-lg shadow-lg overflow-hidden transition-all hover:shadow-xl hover:transform hover:scale-105"
                  style={{backgroundColor: '#faf9f7'}}
                >
                  {/* Member Photo */}
                  <div className="relative">
                    <img 
                      src={member.image} 
                      alt={member.name}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                      <div className="text-white">
                        <h3 className="text-xl font-bold">{member.name}</h3>
                        <p className="text-sm opacity-90">{member.position}</p>
                      </div>
                    </div>
                  </div>

                  {/* Member Info */}
                  <div className="p-6">
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span 
                          className="px-3 py-1 rounded-full text-xs font-semibold"
                          style={{backgroundColor: '#e8ede6', color: '#4a5d3a'}}
                        >
                          {member.position.split(' - ')[0]}
                        </span>
                        <div className="flex items-center space-x-2 text-xs" style={{color: '#6b7c5d'}}>
                          <span>📄 {member.publications}</span>
                          <span>📊 h-{member.hIndex}</span>
                        </div>
                      </div>
                      
                      <div className="text-sm mb-3" style={{color: '#6b7c5d'}}>
                        <p><strong>Institution:</strong> {member.affiliation}</p>
                        <p><strong>Specialization:</strong> {member.specialization}</p>
                        <p><strong>Email:</strong> 
                          <a href={`mailto:${member.email}`} className="ml-1 hover:underline" style={{color: '#6b7c5d'}}>
                            {member.email}
                          </a>
                        </p>
                      </div>
                      
                      <p className="text-sm leading-relaxed" style={{color: '#6b7c5d'}}>
                        {member.bio}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button 
                        className="flex-1 py-2 px-4 rounded-lg text-sm font-semibold transition-colors"
                        style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                        onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                        onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                      >
                        View Profile
                      </button>
                      <button 
                        className="flex-1 py-2 px-4 rounded-lg text-sm font-semibold transition-colors border"
                        style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                        onMouseOver={(e) => {
                          e.target.style.backgroundColor = '#6b7c5d';
                          e.target.style.color = '#f5f5f0';
                        }}
                        onMouseOut={(e) => {
                          e.target.style.backgroundColor = 'transparent';
                          e.target.style.color = '#6b7c5d';
                        }}
                      >
                        Publications
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Editorial Process Info */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  🔍
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Rigorous Review Process
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Our editorial board ensures every submission undergoes thorough double-blind peer review for academic excellence.
                </p>
              </div>
              
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  🌍
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Global Expertise
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Our board members represent leading institutions from around the world, bringing diverse perspectives to legal scholarship.
                </p>
              </div>
              
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  ⚡
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Fast Track Review
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Efficient editorial processes ensure rapid publication without compromising quality standards.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default EditorialBoardNew;
