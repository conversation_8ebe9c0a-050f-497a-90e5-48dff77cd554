import React from 'react'; import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';
import { Container, Typography, Box, Paper, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material'; import { Description, FormatListNumbered, Assignment, Timer, CheckCircle } from '@mui/icons-material';
function InstructionsForScholars() {
    return (
        <>      <Navbar />
            <div className="min-h-screen bg-orange-50">        <Container maxWidth="lg" sx={{ py: 8 }}>
                <Typography variant="h2"
                    component="h1"
                    align="center" gutterBottom sx={{
                        color: 'primary.main', fontWeight: 'bold',
                        mb: 6,
                    }}
                >
                    Instructions for Scholars          </Typography>
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>            <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                    Manuscript Guidelines            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon><Description color="primary" /></ListItemIcon>                <ListItemText
                            primary="Format Requirements" secondary="Manuscripts should be submitted in MS Word format, double-spaced, with 12-point Times New Roman font. Maximum length: 8,000 words including footnotes."
                        />              </ListItem>
                        <ListItem>                <ListItemIcon><FormatListNumbered color="primary" /></ListItemIcon>
                            <ListItemText primary="Citation Style"
                                secondary="Follow the latest edition of The Bluebook: A Uniform System of Citation for all references and citations." />
                        </ListItem>            </List>
                    <Divider sx={{ my: 3 }} />
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                        Submission Process            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon><Assignment color="primary" /></ListItemIcon>                <ListItemText
                            primary="Required Documents" secondary="1. Main manuscript (without author details), 2. Title page with author information, 3. Abstract (250 words), 4. Keywords (5-7)"
                        />              </ListItem>
                        <ListItem>                <ListItemIcon><Timer color="primary" /></ListItemIcon>
                            <ListItemText primary="Review Timeline"
                                secondary="Initial review: 2-3 weeks, Peer review: 4-6 weeks, Final decision: 2 weeks after peer review" />
                        </ListItem>            </List>
                    <Divider sx={{ my: 3 }} />
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                        Publication Ethics            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon><CheckCircle color="primary" /></ListItemIcon>                <ListItemText
                            primary="Ethical Guidelines" secondary="Authors must ensure original work, proper attribution of sources, and disclosure of any conflicts of interest. Plagiarism and duplicate submissions are strictly prohibited."
                        />              </ListItem>
                    </List>          </Paper>
            </Container>      </div>
            <Footer />
        </>
    )
}

export default InstructionsForScholars;
