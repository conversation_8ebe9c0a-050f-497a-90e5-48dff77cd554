import React from 'react'; import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';
import { Container, Typography, Box, Paper, List, ListItem, ListItemIcon, ListItemText } from '@mui/material'; import { Check as CheckIcon, School as SchoolIcon, Gavel as GavelIcon } from '@mui/icons-material';
function AimAndScope() {
    return (
        <>      <Navbar />
            <div className="min-h-screen bg-orange-50">        <Container maxWidth="lg" sx={{ py: 8 }}>
                {/* Page Title */}          <Typography
                    variant="h2" component="h1"
                    align="center" gutterBottom
                    sx={{
                        color: 'primary.main',
                        fontWeight: 'bold', mb: 6,
                    }}          >
                    Ai<PERSON> and Scope          </Typography>
                {/* Main Content */}
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>            <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                    Our Mission            </Typography>
                    <Typography paragraph>              The Journal of Legal Studies (JLS) aims to advance legal scholarship by publishing innovative,
                        high-quality research that contributes to the development of legal theory and practice. We focus               on interdisciplinary approaches that bridge the gap between theoretical frameworks and practical
                        applications in law.            </Typography>
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold" sx={{ mt: 4 }}>
                        Scope of Publication            </Typography>
                    <List>              {[
                        'Constitutional and Administrative Law', 'Criminal Law and Justice',
                        'International Law and Human Rights', 'Environmental Law and Climate Justice',
                        'Corporate and Commercial Law', 'Technology Law and Digital Rights',
                        'Legal Education and Research Methodology', 'Comparative Legal Studies'
                    ].map((item, index) => (<ListItem key={index}>
                        <ListItemIcon>                    <CheckIcon color="primary" />
                        </ListItemIcon>                  <ListItemText primary={item} />
                    </ListItem>
                    ))}            </List>
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold" sx={{ mt: 4 }}>              Publication Standards
                    </Typography>            <Typography paragraph>
                        We maintain rigorous academic standards through:            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon>                  <SchoolIcon color="primary" />
                        </ListItemIcon>                <ListItemText
                            primary="Peer Review Process" secondary="Double-blind peer review by expert scholars in the field"
                        />              </ListItem>
                        <ListItem>                <ListItemIcon>
                            <GavelIcon color="primary" />                </ListItemIcon>
                            <ListItemText primary="Editorial Excellence"
                                secondary="Thorough editorial review ensuring highest quality of published work" />
                        </ListItem>            </List>
                </Paper>        </Container>
            </div>      <Footer />
        </>
    );
}

export default AimAndScope;
