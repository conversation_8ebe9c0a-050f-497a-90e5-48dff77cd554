import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Box,
  ThemeProvider,
  createTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

// Custom theme with orange color scheme
const theme = createTheme({
  palette: {
    primary: {
      main: '#ea580c', // orange-600
      light: '#fb923c', // orange-400
      dark: '#c2410c', // orange-700
    },
    secondary: {
      main: '#fed7aa', // orange-200
    },
  },
});

// Styled Components

const StyledCard = styled(Card)(() => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
  borderRadius: '16px',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
  },
}));


const AdvisoryBoard = () => {
  // Advisory Board Members Data
  const advisoryMembers = [
    {
      name: 'Prof. David <PERSON>',
      title: 'Senior Legal Scholar',
      affiliation: 'Oxford University',
      image: '/path/to/image1.jpg',
      quote: 'Dedicated to advancing legal education and research through innovative methodologies.',
    },
    {
      name: 'Dr. Maria Garcia',
      title: 'International Law Expert',
      affiliation: 'Columbia Law School',
      image: '/path/to/image2.jpg',
      quote: 'Bridging theoretical frameworks with practical legal applications.',
    },
    {
      name: 'Justice Robert Thompson',
      title: 'Former Supreme Court Justice',
      affiliation: 'National Judicial Academy',
      image: '/path/to/image3.jpg',
      quote: 'Committed to upholding legal excellence and judicial integrity.',
    },
    {
      name: 'Prof. Sarah Mitchell',
      title: 'Constitutional Law Expert',
      affiliation: 'Harvard Law School',
      image: '/path/to/image4.jpg',
      quote: 'Advancing constitutional scholarship through rigorous academic research.',
    },
    {
      name: 'Dr. James Wilson',
      title: 'Corporate Law Specialist',
      affiliation: 'Stanford Law School',
      image: '/path/to/image5.jpg',
      quote: 'Bridging the gap between corporate practice and academic excellence.',
    },
    {
      name: 'Prof. Lisa Chen',
      title: 'International Human Rights Advocate',
      affiliation: 'Yale Law School',
      image: '/path/to/image6.jpg',
      quote: 'Championing human rights through legal scholarship and advocacy.',
    },
    // Add more members as needed
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-orange-50">
        <ThemeProvider theme={theme}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
          {/* Page Title */}
          <Typography
            variant="h2"
            component="h1"
            align="center"
            gutterBottom
            sx={{
              color: 'primary.main',
              fontWeight: 'bold',
              mb: 6,
            }}
          >
            Advisory Board
          </Typography>

          {/* Subtitle */}
          <Typography
            variant="h6"
            align="center"
            sx={{
              mb: 6,
              color: 'text.secondary',
              maxWidth: '900px',
              mx: 'auto',
              lineHeight: 1.6
            }}
          >
            Our esteemed advisory board brings together leading experts in law and jurisprudence to guide our editorial vision and maintain the highest academic standards
          </Typography>

          {/* Mission Statement */}
          <Box sx={{ mb: 8, textAlign: 'center' }}>
            <Typography
              variant="body1"
              sx={{
                maxWidth: '700px',
                mx: 'auto',
                color: 'text.secondary',
                fontStyle: 'italic',
                fontSize: '1.1rem',
                lineHeight: 1.7
              }}
            >
              "Committed to advancing legal scholarship through collaborative excellence and innovative research methodologies"
            </Typography>
          </Box>

          {/* Advisory Board Members Grid */}
          <Grid container spacing={4} justifyContent="center" alignItems="stretch">
            {advisoryMembers.map((member, index) => (
              <Grid item xs={12} sm={6} md={4} key={index} sx={{ display: 'flex' }}>
                <StyledCard sx={{ width: '100%' }}>
                  <CardContent
                    sx={{
                      textAlign: 'center',
                      p: 3,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      height: '100%',
                      minHeight: '400px'
                    }}
                  >
                    {/* Top Section - Avatar and Basic Info */}
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                      {/* Member Avatar */}
                      <Avatar
                        src={member.image}
                        alt={member.name}
                        sx={{
                          width: 120,
                          height: 120,
                          mb: 2,
                          border: '4px solid',
                          borderColor: 'primary.main',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                        }}
                      />

                      {/* Member Name */}
                      <Typography
                        variant="h5"
                        component="h2"
                        gutterBottom
                        sx={{
                          fontWeight: 'bold',
                          mb: 1,
                          lineHeight: 1.3,
                          minHeight: '2.6em',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        {member.name}
                      </Typography>

                      {/* Member Title */}
                      <Typography
                        variant="subtitle1"
                        color="primary.main"
                        gutterBottom
                        sx={{
                          fontWeight: 'medium',
                          mb: 1,
                          minHeight: '1.5em',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        {member.title}
                      </Typography>

                      {/* Member Affiliation */}
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        gutterBottom
                        sx={{
                          fontStyle: 'italic',
                          mb: 2,
                          minHeight: '1.2em',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        {member.affiliation}
                      </Typography>
                    </Box>

                    {/* Bottom Section - Quote */}
                    <Box sx={{ mt: 'auto', pt: 2 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontStyle: 'italic',
                          color: 'text.secondary',
                          fontSize: '0.875rem',
                          lineHeight: 1.5,
                          textAlign: 'center',
                          px: 1
                        }}
                      >
                        "{member.quote}"
                      </Typography>
                    </Box>
                  </CardContent>
                </StyledCard>
              </Grid>
            ))}
          </Grid>
          </Container>
        </ThemeProvider>
      </div>
      <Footer />
    </>
  );
};

export default AdvisoryBoard;
