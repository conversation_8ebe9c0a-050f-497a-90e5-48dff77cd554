import './App.css'

import { Route, Routes } from 'react-router-dom'
import HomeNew from './pages/HomeNew'
import ReviewersPanel from './pages/EditorialOraganization/ReviewersPanel'
import Jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './pages/EditorialOraganization/JoinAsReviewer'
import EditorialBoard from './pages/EditorialOraganization/EditorialBoardNew'
import AdvisoryBoard from './pages/EditorialOraganization/AdvisoryBoard'
import PrePrint from './pages/Repository/CurrentIssuse/Pre-Print'
import Authorsversion from './pages/Repository/CurrentIssuse/Authorsversion'

function App() {
  return (
    <Routes>
    {/* Editorial Organization */}
      <Route index element={<HomeNew />} ></Route>
      <Route path='/Editorial Organization/editorial-board' element={<EditorialBoard />}></Route>
      <Route path='/Editorial Organization/reviewers-panel' element={<ReviewersPanel />}></Route>
      <Route path='/Editorial Organization/AdvisoryBoard' element={<AdvisoryBoard />}></Route>
      <Route path='/Editorial Organization/join-reviewer' element={<JoinAsReviewer />}></Route>
    {/* Repository */}
      <Route path='/respository/current/Pre-Print' element={<PrePrint />}></Route>
      <Route path='/respository/current/Authorsversion' element={<Authorsversion />}></Route>




    </Routes>
  )
}

export default App
