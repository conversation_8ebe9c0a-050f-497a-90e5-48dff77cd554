import './App.css'

import { Route, Routes } from 'react-router-dom'
import HomeNew from './pages/HomeNew'
import ReviewersPanel from './pages/EditorialOraganization/ReviewersPanel'
import Join<PERSON><PERSON><PERSON><PERSON><PERSON> from './pages/EditorialOraganization/Join<PERSON>Reviewer'
import EditorialBoard from './pages/EditorialOraganization/EditorialBoardNew'
import AdvisoryBoard from './pages/EditorialOraganization/AdvisoryBoard'
import PrePrint from './pages/Repository/CurrentIssuse/Pre-Print'
import Authorsversion from './pages/Repository/CurrentIssuse/Authorsversion'
import ArchiveArticle from './pages/Repository/ArchiveArticle'
import BookPublished from './pages/Repository/BookPublished'
import AimAndScope from './pages/AboutUs/AimAndScope'
import InstructionsForScholars from './pages/AboutUs/InstructionsforScholars'

function App() {
  return (
    <Routes>
    {/* Editorial Organization */}
      <Route index element={<HomeNew />} ></Route>
      <Route path='/Editorial Organization/editorial-board' element={<EditorialBoard />}></Route>
      <Route path='/Editorial Organization/reviewers-panel' element={<ReviewersPanel />}></Route>
      <Route path='/Editorial Organization/AdvisoryBoard' element={<AdvisoryBoard />}></Route>
      <Route path='/Editorial Organization/join-reviewer' element={<JoinAsReviewer />}></Route>
    {/* Repository */}
      <Route path='/respository/current/Pre-Print' element={<PrePrint />}></Route>
      <Route path='/respository/current/Authorsversion' element={<Authorsversion />}></Route>
      <Route path='/respository/ArchiveArticle' element={<ArchiveArticle />}></Route>
      <Route path='/respository/bookpublished' element={<BookPublished />}></Route>


  {/*About Us  */}
      <Route path='/aboutus/aimscope' element={<AimAndScope />}></Route>
      <Route path='/aboutus/instructions' element={<InstructionsForScholars />}></Route>
      <Route path='/aboutus/manuscript-submission' element={<ManuscriptSubmission />}></Route>  

    </Routes>
  )
}

export default App
