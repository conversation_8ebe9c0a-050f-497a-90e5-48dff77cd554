import './App.css'

import { Route, Routes } from 'react-router-dom'
import HomeNew from './pages/HomeNew'
import EditorialBoardNew from './pages/EditorialOraganization/EditorialBoardNew'
import ReviewersPanel from './pages/EditorialOraganization/ReviewersPanel'
import AdvisoryBoardNew from './pages/EditorialOraganization/AdvisoryBoardNew'
import Jo<PERSON><PERSON>Revie<PERSON> from './pages/EditorialOraganization/JoinAsReviewer'

function App() {
  return (
    <Routes>
      <Route index element={<HomeNew />} ></Route>
      <Route path= '/Editorial Organization/editorial-board'  element={<EditorialBoardNew/>}></Route>
      <Route path= '/Editorial Organization/reviewers-panel'  element={<ReviewersPanel/>}></Route>
      <Route path= '/Editorial Organization/AdvisoryBoard'  element={<AdvisoryBoardNew/>}></Route>
      <Route path= '/Editorial Organization/join-as-reviewer'  element={<JoinAsReviewer/>}></Route>



      


    </Routes>
  )
}

export default App
