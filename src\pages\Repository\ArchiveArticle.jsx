import { useState } from 'react';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

function ArchiveArticle() {
  const [searchTerm, setSearchTerm] = useState('');

  const archiveArticles = [
    {
      id: 1,
      title: "Historical Development of International Law",
      authors: "Prof. <PERSON>, Dr. <PERSON>",
      publicationDate: "January 2023",
      volume: "Vol. 12, Issue 1",
      abstract: "A comprehensive analysis of the evolution of international law from the 20th century to present day...",
      keywords: ["International Law", "Legal History", "Global Governance"],
      citations: 45,
      downloads: 1200
    },
    {
      id: 2,
      title: "Comparative Analysis of Constitutional Rights",
      authors: "Dr. <PERSON>, Prof. <PERSON>",
      publicationDate: "March 2023",
      volume: "Vol. 12, Issue 2",
      abstract: "This study examines the variations in constitutional rights across different legal systems...",
      keywords: ["Constitutional Law", "Comparative Law", "Human Rights"],
      citations: 38,
      downloads: 950
    }
    // Add more archive articles as needed
  ];

  const filteredArticles = archiveArticles.filter(article =>
    article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.keywords.some(keyword =>
      keyword.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f5f5f0]">
        {/* Header Section */}
        <div className="bg-[#faf9f7] py-8 px-4">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold text-[#4a5d3a] mb-4">Archive Articles</h1>
            <p className="text-[#6b7c5d] mb-6">
              Browse through our collection of previously published articles
            </p>
            {/* Search Bar */}
            <input
              type="text"
              placeholder="Search archive articles..."
              className="w-full max-w-2xl px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-[#4a5d3a]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Archive Articles List */}
        <div className="max-w-7xl mx-auto py-8 px-4">
          <div className="space-y-6">
            {filteredArticles.map(article => (
              <div
                key={article.id}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              >
                <h2 className="text-xl font-semibold text-[#4a5d3a] mb-2">{article.title}</h2>
                <p className="text-sm text-gray-600 mb-2">{article.authors}</p>
                <p className="text-sm text-gray-500 mb-2">
                  Published: {article.publicationDate} | {article.volume}
                </p>
                <p className="text-gray-700 mb-4">{article.abstract}</p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.keywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-[#e8ede6] text-[#4a5d3a] rounded-full text-sm"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex space-x-4 text-sm text-[#6b7c5d]">
                    <span>📚 {article.citations} citations</span>
                    <span>⬇️ {article.downloads} downloads</span>
                  </div>
                  <button className="px-4 py-2 rounded-lg text-sm font-semibold bg-[#6b7c5d] text-white hover:bg-[#4a5d3a]">
                    Read Full Article
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default ArchiveArticle;
