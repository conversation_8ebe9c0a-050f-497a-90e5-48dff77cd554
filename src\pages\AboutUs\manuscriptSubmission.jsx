import React from 'react'; import Navbar from '../../Components/Navbar';
import { Container, Typography, Paper, Box, List, ListItem, ListItemIcon, ListItemText, Button, Alert } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles'; import { Description, CheckCircle, AttachFile } from '@mui/icons-material';
const theme = createTheme({
    palette: {
        primary: {
            main: '#ea580c',
        },
    },
});
function ManuscriptSubmission() {
    return (
        <>      <Navbar />
            <div className="min-h-screen bg-orange-50">        <ThemeProvider theme={theme}>
                <Container maxWidth="lg" sx={{ py: { xs: 4, md: 8 }, px: { xs: 2, sm: 3 } }}>            <Typography
                    variant="h2" component="h1"
                    align="center" gutterBottom
                    sx={{
                        color: 'primary.main',
                        fontWeight: 'bold',
                        mb: { xs: 4, md: 6 },
                    }}            >
                    Manuscript Submission Guidelines            </Typography>
                    <Alert severity="info" sx={{ mb: 4 }}>
                        <Typography variant="body1">                Before submitting your manuscript, please ensure that you have read our submission guidelines carefully.
                        </Typography>            </Alert>
                    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
                        <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">                Submission Requirements
                        </Typography>              <List>
                            <ListItem>                  <ListItemIcon>
                                <CheckCircle color="primary" />                  </ListItemIcon>
                                <ListItemText primary="Manuscript Format"
                                    secondary="Submit in Microsoft Word (.doc or .docx) format, double-spaced with 1-inch margins" />
                            </ListItem>                <ListItem>
                                <ListItemIcon>                    <CheckCircle color="primary" />
                                </ListItemIcon>                  <ListItemText
                                    primary="Length" secondary="Articles should be between 5,000 and 10,000 words, including footnotes and references"
                                />                </ListItem>
                            <ListItem>                  <ListItemIcon>
                                <CheckCircle color="primary" />                  </ListItemIcon>
                                <ListItemText primary="Citation Style"
                                    secondary="Follow the latest edition of The Bluebook: A Uniform System of Citation" />
                            </ListItem>
                        </List>            </Paper>
                    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>              <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                        Submission Process              </Typography>
                        <Box sx={{ mb: 3 }}>                <Typography paragraph>
                            To submit your manuscript, please follow these steps:                </Typography>
                            <List>                  <ListItem>
                                <ListItemIcon>                      <Description />
                                </ListItemIcon>                    <ListItemText
                                    primary="1. Prepare Your Manuscript" secondary="Ensure your manuscript meets all formatting requirements"
                                />                  </ListItem>
                                <ListItem>                    <ListItemIcon>
                                    <AttachFile />                    </ListItemIcon>
                                    <ListItemText primary="2. Submit Online"
                                        secondary="Use our online submission system to upload your manuscript" />
                                </ListItem>                </List>
                        </Box>              <Button
                            variant="contained" color="primary"
                            size="large" sx={{ mt: 2 }}
                        >                Submit Manuscript
                        </Button>            </Paper>
                </Container>        </ThemeProvider>
            </div>    </>
    );
}

export default ManuscriptSubmission;
