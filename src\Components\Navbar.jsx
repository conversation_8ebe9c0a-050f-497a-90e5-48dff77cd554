import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Navbar = () => {
  const [openDropdown, setOpenDropdown] = useState(null);
  const handleMouseEnter = (menu) => setOpenDropdown(menu);
  const handleMouseLeave = () => setOpenDropdown(null);

  return (
    <nav className="bg-[#4a5d3a] text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-xl font-bold">MyJournal</Link>

          <ul className="flex space-x-8 items-center relative">
            <li><Link to="/" className="hover:text-green-300">Home</Link></li>

            {/* Editorial Organization */}
            <li
              onMouseEnter={() => handleMouseEnter('editorial')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <Link to="" className="cursor-pointer hover:text-green-300">
                Editorial Organization
              </Link>
              {openDropdown === 'editorial' && (
                <ul
                  className="absolute top-full left-0 mt-0 bg-[#faf9f7] text-[#4a5d3a] rounded shadow-lg min-w-[14rem] z-50"
                  onMouseEnter={() => handleMouseEnter('editorial')}
                  onMouseLeave={handleMouseLeave}
                >
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/editorial-board">Editorial Board</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/reviewers-panel">Reviewers Panel</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/advisory-board">Advisory Board</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/join-reviewer">Join as Reviewer</Link></li>
                </ul>
              )}
            </li>

            {/* Repository */}
            <li className="relative">
              <div
                onMouseEnter={() => handleMouseEnter('repository')}
                onMouseLeave={handleMouseLeave}
                className="relative"
              >
                <Link to="" className="cursor-pointer hover:text-green-300">
                  Repository
                </Link>
                {openDropdown === 'repository' && (
                  <ul
                    className="absolute top-full left-0 mt-0 bg-white text-[#2e3d2f] rounded-lg shadow-lg min-w-[14rem] z-50 font-medium text-center"
                    onMouseEnter={() => handleMouseEnter('repository')}
                    onMouseLeave={handleMouseLeave}
                  >
                    <li className="relative group">
                      <div className="flex justify-between items-center px-4 py-3 hover:bg-gray-100 cursor-pointer rounded-t-md">
                        <span className="w-full text-center">Current Issue</span>
                      </div>

                      {/* Nested Submenu */}
                      <ul className="absolute top-0 left-full mt-0 hidden group-hover:block bg-white text-[#2e3d2f] rounded-lg shadow-lg min-w-[10rem] z-50 text-center text-sm">
                        <li className="px-3 py-2 hover:bg-gray-100">
                          <Link to="/repository/current/authors">Authors</Link>
                        </li>
                        <li className="px-3 py-2 hover:bg-gray-100">
                          <Link to="/repository/current/preprint">Pre-print</Link>
                        </li>
                        <li className="px-3 py-2 hover:bg-gray-100">
                          <Link to="/repository/current/versions">Author's Versions</Link>
                        </li>
                      </ul>

                    </li>

                    <li className="px-4 py-3 hover:bg-gray-100">
                      <Link to="/repository/archive-articles">Archive Articles</Link>
                    </li>
                    <li className="px-4 py-3 hover:bg-gray-100 rounded-b-md">
                      <Link to="/repository/books">Books Published</Link>
                    </li>
                  </ul>
                )}
              </div>
            </li>


            {/* About */}
            <li
              onMouseEnter={() => handleMouseEnter('about')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <Link to="/about" className="cursor-pointer hover:text-green-300">About Us</Link>
              {openDropdown === 'about' && (
                <ul
                  className="absolute top-full left-0 mt-0 bg-[#faf9f7] text-[#4a5d3a] rounded shadow-lg min-w-[16rem] z-50"
                  onMouseEnter={() => handleMouseEnter('about')}
                  onMouseLeave={handleMouseLeave}
                >
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/aim-scope">Aim and Scope</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/instructions">Instructions for Scholars</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/manuscript-submission">Manuscript Submission</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/editorial-process">Editorial Process</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/publishing-cost">Publishing Cost</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/publisher-info">Publisher Information</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/subscription">Subscription</Link></li>
                </ul>
              )}
            </li>

            {/* Policies */}
            <li
              onMouseEnter={() => handleMouseEnter('policies')}
              onMouseLeave={handleMouseLeave}
              className="relative"
            >
              <Link to="/policies" className="cursor-pointer hover:text-green-300">
                Policies & Guidelines
              </Link>
              {openDropdown === 'policies' && (
                <ul
                  className="absolute top-full left-0 mt-0 bg-[#faf9f7] text-[#4a5d3a] rounded shadow-lg min-w-[18rem] z-50"
                  onMouseEnter={() => handleMouseEnter('policies')}
                  onMouseLeave={handleMouseLeave}
                >
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/publication-policies">Publication Policies</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/open-access">Open Access Policies</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/copyright-policies">Copyright Policies</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/licensing">Licensing Policies</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/plagiarism">Plagiarism Policy</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/ethics">Ethics Policy</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/cancellation-refund">Cancellation and Refund</Link></li>
                  <li className="px-4 py-2 hover:bg-gray-100"><Link to="/guidelines">Work and Other Guidelines</Link></li>
                </ul>
              )}
            </li>

            <li><Link to="/contact" className="hover:text-green-300">Contact</Link></li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
