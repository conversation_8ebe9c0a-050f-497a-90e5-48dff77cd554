import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X } from 'lucide-react';

const Navbar = () => {
  const [openDropdown, setOpenDropdown] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCurrentIssueOpen, setIsCurrentIssueOpen] = useState(false); // Specific to "Current Issue"

  const handleMouseEnter = (menu) => !isMobileMenuOpen && setOpenDropdown(menu);
  const handleMouseLeave = () => {
    if (!isMobileMenuOpen) {
      setOpenDropdown(null);
      setIsCurrentIssueOpen(false);
    }
  };
  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);

  return (
    <nav className="bg-[#4a5d3a] text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="text-xl font-bold">MyJournal</Link>

          {/* Hamburger Button */}
          <button onClick={toggleMobileMenu} className="md:hidden">
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* Desktop Menu */}
          <ul className="hidden md:flex space-x-8 items-center relative">
            <li><Link to="/" className="hover:text-green-300">Home</Link></li>

            {/* Dropdown Menus */}
            {['editorial', 'repository', 'about', 'policies'].map(menu => (
              <li
                key={menu}
                onMouseEnter={() => handleMouseEnter(menu)}
                onMouseLeave={handleMouseLeave}
                className="relative"
              >
                <span className="cursor-pointer hover:text-green-300 capitalize">{menu}</span>

                {openDropdown === menu && (
                  <ul className="absolute top-full left-1/2 -translate-x-1/2 mt-0 bg-[#faf9f7] text-[#4a5d3a] rounded shadow-lg min-w-[14rem] z-50 text-center">
                    {menu === 'editorial' && (
                      <>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/Editorial Organization/editorial-board">Editorial Board</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/Editorial Organization/reviewers-panel">Reviewers Panel</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/Editorial Organization/AdvisoryBoard">Advisory Board</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/Editorial Organization/join-reviewer">Join as Reviewer</Link></li>
                      </>
                    )}
                    {menu === 'repository' && (
                      <li
                        className="relative text-left group"
                        onMouseEnter={() => setIsCurrentIssueOpen(true)}
                        onMouseLeave={() => setIsCurrentIssueOpen(false)}
                      >
                        <div className="px-4 py-2 font-semibold hover:bg-gray-100 cursor-pointer flex justify-between items-center">
                          Current Issue
                          <span className="ml-2"></span>
                        </div>

                        {/* Submenu for Current Issue */}
                        {isCurrentIssueOpen && (
                          <ul className="absolute left-full top-0 ml-1 w-64 bg-white text-[#4a5d3a] shadow-xl rounded-xl border border-gray-200 z-50">
                            <li className="px-4 py-2 hover:bg-[#f0f0f0] transition-all duration-150">
                              <Link to="/respository/current/Authorsversion">Author's Versions</Link>
                            </li>
                            <li className="px-4 py-2 hover:bg-[#f0f0f0] transition-all duration-150">
                              <Link to="/respository/current/Pre-Print">Pre-print</Link>
                            </li>
                          </ul>
                        )}

                        {/* Other repository links */}
                        <ul className="bg-white text-[#4a5d3a] rounded-b-lg shadow-md mt-1 border border-t-0 border-gray-200 text-left z-40">
                          
                          <li className="px-4 py-2 hover:bg-[#f0f0f0] transition-all duration-150">
                            <Link to="/repository/archive-articles">Archive Articles</Link>
                          </li>
                          <li className="px-4 py-2 hover:bg-[#f0f0f0] transition-all duration-150">
                            <Link to="/repository/books">Books Published</Link>
                          </li>
                        </ul>
                      </li>
                    )}


                    {menu === 'about' && (
                      <>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/aim-scope">Aim and Scope</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/instructions">Instructions for Scholars</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/manuscript-submission">Manuscript Submission</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/editorial-process">Editorial Process</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/publisher-info">Publisher Info</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/subscription">Subscription</Link></li>
                      </>
                    )}

                    {menu === 'policies' && (
                      <>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/publication-policies">Publication Policies</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/open-access">Open Access</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/copyright-policies">Copyright</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/licensing">Licensing</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/plagiarism">Plagiarism</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/ethics">Ethics</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/cancellation-refund">Cancellation & Refund</Link></li>
                        <li className="px-4 py-2 hover:bg-gray-100"><Link to="/guidelines">Other Guidelines</Link></li>
                      </>
                    )}
                  </ul>
                )}
              </li>
            ))}

            <li><Link to="/contact" className="hover:text-green-300">Contact</Link></li>
          </ul>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden flex flex-col gap-2 py-4 text-[#4a5d3a] bg-white rounded shadow-md px-4">
            <Link to="/" className="py-2 border-b">Home</Link>
            <Dropdown title="Editorial Organization" links={[
              { label: 'Editorial Board', to: '/Editorial Organization/editorial-board' },
              { label: 'Reviewers Panel', to: '/Editorial Organization/reviewers-panel' },
              { label: 'Advisory Board', to: '/Editorial Organization/AdvisoryBoard' },
              { label: 'Join as Reviewer', to: '/Editorial Organization/join-reviewer' }
            ]} />
            <Dropdown title="Repository" links={[
              { label: 'Authors', to: '/repository/current/authors' },
              { label: 'Pre-print', to: '/repository/current/preprint' },
              { label: "Author's Versions", to: '/repository/current/versions' },
              { label: 'Archive Articles', to: '/repository/archive-articles' },
              { label: 'Books Published', to: '/repository/books' },
            ]} />
            <Dropdown title="About Us" links={[
              { label: 'Aim and Scope', to: '/aim-scope' },
              { label: 'Instructions for Scholars', to: '/instructions' },
              { label: 'Manuscript Submission', to: '/manuscript-submission' },
              { label: 'Editorial Process', to: '/editorial-process' },
              { label: 'Publisher Info', to: '/publisher-info' },
              { label: 'Subscription', to: '/subscription' },
            ]} />
            <Dropdown title="Policies" links={[
              { label: 'Publication Policies', to: '/publication-policies' },
              { label: 'Open Access', to: '/open-access' },
              { label: 'Copyright', to: '/copyright-policies' },
              { label: 'Licensing', to: '/licensing' },
              { label: 'Plagiarism', to: '/plagiarism' },
              { label: 'Ethics', to: '/ethics' },
              { label: 'Cancellation & Refund', to: '/cancellation-refund' },
              { label: 'Other Guidelines', to: '/guidelines' },
            ]} />
            <Link to="/contact" className="py-2">Contact</Link>
          </div>
        )}
      </div>
    </nav>
  );
};

const Dropdown = ({ title, links }) => (
  <div>
    <p className="py-2 font-semibold">{title}</p>
    <ul className="pl-4">
      {links.map((item, idx) => (
        <li key={idx}>
          <Link to={item.to} className="block py-1">{item.label}</Link>
        </li>
      ))}
    </ul>
  </div>
);

export default Navbar;
