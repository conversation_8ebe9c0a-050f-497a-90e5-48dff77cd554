import { useState } from 'react';
import Navbar from '../../../Components/Navbar'; import Footer from '../../../Components/Footer';
function Authors() {
    const [searchTerm, setSearchTerm] = useState('');
    const authors = [{
        id: 1, name: "Dr. <PERSON>",
        affiliation: "Harvard Law School", publications: 12,
        expertise: ["Constitutional Law", "Digital Rights", "Cyber Law"],
        bio: "<PERSON><PERSON> specializes in digital rights and constitutional law, with a focus on emerging technology regulations.", recentArticle: "The Evolution of Digital Rights in Cyberlaw"
    },
    {
        id: 2,
        name: "Prof. <PERSON>", affiliation: "Yale Law School",
        publications: 15, expertise: ["Environmental Law", "Climate Litigation", "International Law"],
        bio: "<PERSON><PERSON> is a leading expert in environmental law and climate change litigation.", recentArticle: "Environmental Law and Climate Change Litigation"
    }, {
        id: 3, name: "Dr. <PERSON>",
        affiliation: "Stanford Law School", publications: 8,
        expertise: ["Human Rights Law", "International Criminal Law"], bio: "<PERSON><PERSON> focuses on human rights law and international criminal justice.",
        recentArticle: "Modern Approaches to Human Rights Law"
    }];
    const filteredAuthors = authors.filter(author => author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        author.expertise.some(exp => exp.toLowerCase().includes(searchTerm.toLowerCase())));
    return (
        <>            <Navbar />
            <div className="min-h-screen bg-[#f5f5f0]">                {/* Header Section */}
                <div className="bg-[#faf9f7] py-8 px-4">                    <div className="max-w-7xl mx-auto">
                    <h1 className="text-3xl font-bold text-[#4a5d3a] mb-4">Contributing Authors</h1>                        <p className="text-[#6b7c5d] mb-6">
                        Discover our distinguished authors and their contributions to legal scholarship                        </p>
                    {/* Search Bar */}                        <input
                        type="text" placeholder="Search authors by name or expertise..."
                        className="w-full max-w-2xl px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-[#4a5d3a]" value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)} />
                </div>                </div>
                {/* Authors List */}
                <div className="max-w-7xl mx-auto py-8 px-4">                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredAuthors.map(author => (<div key={author.id} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                        <h2 className="text-xl font-semibold text-[#4a5d3a] mb-2">{author.name}</h2>                                <p className="text-sm text-gray-600 mb-2">{author.affiliation}</p>
                        <p className="text-sm text-gray-500 mb-4">Publications: {author.publications}</p>                                <p className="text-gray-700 mb-4">{author.bio}</p>
                        <div className="mb-4">                                    <p className="text-sm font-semibold text-[#4a5d3a] mb-2">Areas of Expertise:</p>
                            <div className="flex flex-wrap gap-2">                                        {author.expertise.map((exp, index) => (
                                <span key={index} className="px-3 py-1 bg-[#e8ede6] text-[#4a5d3a] rounded-full text-sm">                                                {exp}
                                </span>))}
                            </div>                                </div>
                        <div className="mt-4 pt-4 border-t border-gray-200">                                    <p className="text-sm text-[#4a5d3a]">
                            <strong>Recent Article:</strong> {author.recentArticle}                                    </p>
                        </div>                            </div>
                    ))}                    </div>
                </div>            </div>
            <Footer />        </>
    );
}

export default Authors;
