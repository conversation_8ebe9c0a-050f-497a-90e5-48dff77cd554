
import { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Box,
  Alert,
  Chip,
  Paper,
  ThemeProvider,
  createTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

// Custom theme with orange color scheme
const theme = createTheme({
  palette: {
    primary: {
      main: '#ea580c', // orange-600
      light: '#fb923c', // orange-400
      dark: '#c2410c', // orange-700
    },
    secondary: {
      main: '#fed7aa', // orange-200
    },
  },
});

// Styled Components
const StyledCard = styled(Card)(() => ({
  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
  borderRadius: '16px',
  overflow: 'hidden',
  border: '1px solid rgba(234, 88, 12, 0.1)',
  height: '100%',
}));

const StyledButton = styled(But<PERSON>)(() => ({
  background: 'linear-gradient(45deg, #ea580c 30%, #fb923c 90%)',
  borderRadius: '12px',
  padding: '14px 40px',
  fontSize: '1.1rem',
  fontWeight: 'bold',
  textTransform: 'none',
  boxShadow: '0 4px 20px rgba(234, 88, 12, 0.3)',
  minWidth: '200px',
  '&:hover': {
    background: 'linear-gradient(45deg, #c2410c 30%, #ea580c 90%)',
    boxShadow: '0 6px 25px rgba(234, 88, 12, 0.4)',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.3s ease-in-out',
}));

const BenefitCard = styled(Paper)(() => ({
  padding: '32px 24px',
  textAlign: 'center',
  borderRadius: '16px',
  border: '1px solid rgba(234, 88, 12, 0.1)',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  minHeight: '280px',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-6px)',
    boxShadow: '0 12px 30px rgba(0,0,0,0.15)',
  },
}));

const StyledTextField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '12px',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 1)',
    },
    '&.Mui-focused': {
      backgroundColor: 'rgba(255, 255, 255, 1)',
    },
  },
  '& .MuiInputLabel-root': {
    fontWeight: '500',
  },
}));

const JoinAsReviewer = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    expertise: '',
    profileLink: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  const benefits = [
    {
      title: 'Contribute to Scholarship',
      description: 'Shape the future of legal research by reviewing cutting-edge manuscripts and advancing academic discourse',
      icon: '📚'
    },
    {
      title: 'Build Professional Network',
      description: 'Connect with leading academics and practitioners in your field from around the world',
      icon: '🤝'
    },
    {
      title: 'Enhance Credibility',
      description: 'Get recognized as an expert reviewer in your area of specialization and build your reputation',
      icon: '⭐'
    },
    {
      title: 'Stay Current',
      description: 'Access the latest research and developments in legal studies before they are published',
      icon: '🔍'
    },
    {
      title: 'Professional Recognition',
      description: 'Receive certificates and acknowledgments for your contributions to academic excellence',
      icon: '🏆'
    },
    {
      title: 'Flexible Commitment',
      description: 'Review papers on your schedule with reasonable deadlines that fit your professional life',
      icon: '⏰'
    }
  ];

  const expertiseAreas = [
    'Constitutional Law',
    'Criminal Law',
    'Corporate Law',
    'International Law',
    'Human Rights Law',
    'Environmental Law',
    'Intellectual Property',
    'Administrative Law',
    'Family Law',
    'Tax Law',
    'Labor Law',
    'Maritime Law'
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.expertise.trim()) {
      newErrors.expertise = 'Area of expertise is required';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Please tell us about your background and interest';
    } else if (formData.message.trim().length < 50) {
      newErrors.message = 'Please provide at least 50 characters describing your background';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Here you would typically send the data to your backend
      console.log('Form submitted:', formData);
      setIsSubmitted(true);

      // Reset form after successful submission
      setTimeout(() => {
        setFormData({
          name: '',
          email: '',
          expertise: '',
          profileLink: '',
          message: ''
        });
        setIsSubmitted(false);
      }, 5000);
    }
  };

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-orange-50">
        <ThemeProvider theme={theme}>
          {/* Hero Section */}
          <Box sx={{
            background: 'linear-gradient(135deg, #ea580c 0%, #fb923c 100%)',
            color: 'white',
            py: { xs: 6, md: 10 }
          }}>
            <Container maxWidth="lg">
              <Box textAlign="center" sx={{ px: { xs: 2, md: 0 } }}>
                <Typography
                  variant="h2"
                  component="h1"
                  gutterBottom
                  sx={{
                    fontWeight: 'bold',
                    mb: 4,
                    fontSize: { xs: '2.2rem', sm: '2.8rem', md: '3.5rem' },
                    lineHeight: { xs: 1.2, md: 1.1 }
                  }}
                >
                  Join Our Expert Review Panel
                </Typography>

                <Typography
                  variant="h5"
                  sx={{
                    maxWidth: '900px',
                    mx: 'auto',
                    lineHeight: 1.6,
                    opacity: 0.95,
                    fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
                    px: { xs: 1, md: 0 }
                  }}
                >
                  Become part of an elite community of legal scholars and practitioners
                  shaping the future of academic publishing
                </Typography>
              </Box>
            </Container>
          </Box>

          <Container maxWidth="lg" sx={{ py: { xs: 6, md: 10 } }}>
            {/* Benefits Section */}
            <Box sx={{ mb: { xs: 8, md: 12 } }}>
              <Typography
                variant="h3"
                component="h2"
                align="center"
                gutterBottom
                sx={{
                  color: 'primary.main',
                  fontWeight: 'bold',
                  mb: { xs: 4, md: 8 },
                  fontSize: { xs: '2rem', md: '2.5rem' }
                }}
              >
                Why Join as a Reviewer?
              </Typography>

              <Grid container spacing={{ xs: 3, md: 4 }} justifyContent="center" alignItems="stretch">
                {benefits.map((benefit, index) => (
                  <Grid
                    item
                    key={index}
                    xs={12}
                    sm={6}
                    md={4} // 12 / 4 = 3 items per row at md and above
                    sx={{ display: 'flex' }}
                  >
                    <BenefitCard sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                      <Box>
                        <Typography variant="h2" sx={{ mb: 3, fontSize: '3rem', lineHeight: 1 }}>
                          {benefit.icon}
                        </Typography>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            minHeight: '3rem',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          {benefit.title}
                        </Typography>
                      </Box>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          lineHeight: 1.6,
                          fontSize: '0.95rem',
                          mt: 2
                        }}
                      >
                        {benefit.description}
                      </Typography>
                    </BenefitCard>
                  </Grid>
                ))}
              </Grid>
            </Box>

            {/* Application Form Section */}
            <Box sx={{ mb: { xs: 8, md: 12 } }}>
              <Grid container justifyContent="center">
                <Grid item xs={12} lg={10} xl={8}>
                  <StyledCard>
                    <CardContent sx={{ p: { xs: 3, md: 5 } }}>
                      <Typography
                        variant="h4"
                        component="h2"
                        align="center"
                        gutterBottom
                        sx={{
                          color: 'primary.main',
                          fontWeight: 'bold',
                          mb: { xs: 3, md: 5 },
                          fontSize: { xs: '1.8rem', md: '2.2rem' }
                        }}
                      >
                        Application Form
                      </Typography>

                      {isSubmitted && (
                        <Alert
                          severity="success"
                          sx={{
                            mb: 4,
                            borderRadius: '12px',
                            fontSize: '1rem',
                            '& .MuiAlert-message': {
                              padding: '8px 0'
                            }
                          }}
                        >
                          Thank you for your application! We will review your submission and contact you within 5-7 business days.
                        </Alert>
                      )}

                      <form onSubmit={handleSubmit}>
                        <Grid container spacing={{ xs: 3, md: 4 }}>
                          {/* Name and Email Row */}
                          <Grid item xs={12} md={6}>
                            <StyledTextField
                              fullWidth
                              label="Full Name *"
                              name="name"
                              value={formData.name}
                              onChange={handleInputChange}
                              error={!!errors.name}
                              helperText={errors.name}
                              variant="outlined"
                              size="medium"
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <StyledTextField
                              fullWidth
                              label="Email Address *"
                              name="email"
                              type="email"
                              value={formData.email}
                              onChange={handleInputChange}
                              error={!!errors.email}
                              helperText={errors.email}
                              variant="outlined"
                              size="medium"
                            />
                          </Grid>

                          {/* Area of Expertise */}
                          <Grid item xs={12}>
                            <StyledTextField
                              fullWidth
                              label="Primary Area of Expertise *"
                              name="expertise"
                              value={formData.expertise}
                              onChange={handleInputChange}
                              error={!!errors.expertise}
                              helperText={errors.expertise || "e.g., Constitutional Law, Corporate Law, International Law"}
                              variant="outlined"
                              size="medium"
                            />

                            {/* Expertise Suggestions */}
                            <Box sx={{ mt: 3 }}>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  mb: 2,
                                  fontWeight: '500'
                                }}
                              >
                                Popular areas of expertise (click to select):
                              </Typography>
                              <Box sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: 1.5,
                                justifyContent: { xs: 'center', md: 'flex-start' }
                              }}>
                                {expertiseAreas.slice(0, 8).map((area) => (
                                  <Chip
                                    key={area}
                                    label={area}
                                    size="medium"
                                    variant="outlined"
                                    onClick={() => setFormData(prev => ({ ...prev, expertise: area }))}
                                    sx={{
                                      cursor: 'pointer',
                                      borderRadius: '20px',
                                      fontSize: '0.875rem',
                                      height: '36px',
                                      '&:hover': {
                                        backgroundColor: 'primary.main',
                                        color: 'white',
                                        borderColor: 'primary.main'
                                      },
                                      transition: 'all 0.2s ease-in-out'
                                    }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          </Grid>

                          {/* Profile Link */}
                          <Grid item xs={12}>
                            <StyledTextField
                              fullWidth
                              label="LinkedIn Profile / Academic Profile URL"
                              name="profileLink"
                              value={formData.profileLink}
                              onChange={handleInputChange}
                              variant="outlined"
                              helperText="Optional: Link to your professional profile or academic page"
                              size="medium"
                            />
                          </Grid>

                          {/* Message */}
                          <Grid item xs={12}>
                            <StyledTextField
                              fullWidth
                              label="Tell us about yourself *"
                              name="message"
                              value={formData.message}
                              onChange={handleInputChange}
                              error={!!errors.message}
                              helperText={errors.message || "Please describe your background, experience, and why you'd like to join our review panel (minimum 50 characters)"}
                              variant="outlined"
                              multiline
                              rows={5}
                              size="medium"
                            />
                          </Grid>

                          {/* Submit Button */}
                          <Grid item xs={12}>
                            <Box
                              textAlign="center"
                              sx={{
                                mt: { xs: 2, md: 4 },
                                mb: 2
                              }}
                            >
                              <StyledButton
                                type="submit"
                                variant="contained"
                                size="large"
                                disabled={isSubmitted}
                              >
                                {isSubmitted ? 'Application Submitted!' : 'Apply Now'}
                              </StyledButton>
                            </Box>
                          </Grid>
                        </Grid>
                      </form>
                    </CardContent>
                  </StyledCard>
                </Grid>
              </Grid>
            </Box>

            {/* Process Timeline Section */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography
                variant="h4"
                gutterBottom
                sx={{
                  color: 'primary.main',
                  fontWeight: 'bold',
                  mb: { xs: 4, md: 6 },
                  fontSize: { xs: '1.8rem', md: '2.2rem' }
                }}
              >
                What Happens Next?
              </Typography>
              <Grid container spacing={{ xs: 4, md: 6 }} justifyContent="center">
                <Grid item xs={12} md={4}>
                  <Box sx={{
                    p: { xs: 3, md: 4 },
                    borderRadius: '16px',
                    backgroundColor: 'rgba(63, 81, 181, 0.05)',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        color: 'primary.main',
                        fontWeight: 'bold',
                        mb: 2
                      }}
                    >
                      1. Application Review
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      Our editorial team will review your application and qualifications within 5-7 business days
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{
                    p: { xs: 3, md: 4 },
                    borderRadius: '16px',
                    backgroundColor: 'rgba(63, 81, 181, 0.05)',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        color: 'primary.main',
                        fontWeight: 'bold',
                        mb: 2
                      }}
                    >
                      2. Interview Process
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      Selected candidates will be invited for a brief interview to discuss expertise and expectations
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{
                    p: { xs: 3, md: 4 },
                    borderRadius: '16px',
                    backgroundColor: 'rgba(63, 81, 181, 0.05)',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center'
                  }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        color: 'primary.main',
                        fontWeight: 'bold',
                        mb: 2
                      }}
                    >
                      3. Welcome & Onboarding
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{ lineHeight: 1.6 }}
                    >
                      Successful applicants will receive orientation materials and their first review assignments
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Container>
        </ThemeProvider>
      </div>
      <Footer />
    </>
  );
};

export default JoinAsReviewer;
