import Navbar from '../Components/Navbar';
import Footer from '../Components/Footer';

function CurrentIssue() {
  const currentIssueData = {
    volume: "Vol. 15",
    issue: "Issue 2",
    date: "December 2024",
    coverImage: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=600&fit=crop",
    description: "This issue features cutting-edge research in constitutional law, international trade, and legal technology."
  };

  const articles = [
    {
      id: 1,
      title: "Constitutional Interpretation in the Digital Age: Privacy Rights and Technological Surveillance",
      authors: "Dr. <PERSON>, Prof. <PERSON>",
      pages: "1-25",
      doi: "10.1234/jls.2024.001",
      type: "Research Article",
      abstract: "This article examines the evolving landscape of constitutional privacy rights in response to advancing surveillance technologies...",
      keywords: ["Constitutional Law", "Privacy Rights", "Digital Surveillance", "Fourth Amendment"],
      downloads: 245,
      views: 1250
    },
    {
      id: 2,
      title: "International Trade Law and Environmental Protection: Reconciling Economic and Ecological Interests",
      authors: "<PERSON><PERSON> <PERSON>, Dr. <PERSON>",
      pages: "26-48",
      doi: "10.1234/jls.2024.002",
      type: "Research Article",
      abstract: "This study analyzes the tension between international trade agreements and environmental protection measures...",
      keywords: ["International Trade", "Environmental Law", "WTO", "Sustainability"],
      downloads: 189,
      views: 890
    },
    {
      id: 3,
      title: "Artificial Intelligence and Legal Decision-Making: Ethical Considerations for Judicial Systems",
      authors: "Dr. <PERSON>, Prof. Lisa Anderson",
      pages: "49-72",
      doi: "10.1234/jls.2024.003",
      type: "Research Article",
      abstract: "As AI systems become increasingly integrated into legal processes, this article explores the ethical implications...",
      keywords: ["Artificial Intelligence", "Legal Ethics", "Judicial Decision-Making", "Technology Law"],
      downloads: 312,
      views: 1450
    },
    {
      id: 4,
      title: "Corporate Social Responsibility and Legal Accountability: A Comparative Analysis",
      authors: "Prof. Emma Wilson",
      pages: "73-95",
      doi: "10.1234/jls.2024.004",
      type: "Research Article",
      abstract: "This comparative study examines how different legal systems approach corporate social responsibility...",
      keywords: ["Corporate Law", "Social Responsibility", "Comparative Law", "Business Ethics"],
      downloads: 156,
      views: 720
    },
    {
      id: 5,
      title: "Human Rights Law in the 21st Century: Challenges and Opportunities",
      authors: "Dr. Ahmed Hassan, Prof. Priya Sharma",
      pages: "96-118",
      doi: "10.1234/jls.2024.005",
      type: "Review Article",
      abstract: "This comprehensive review examines the evolution of human rights law and emerging challenges...",
      keywords: ["Human Rights", "International Law", "Legal Evolution", "Global Justice"],
      downloads: 203,
      views: 980
    }
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        
        {/* Header Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Current Issue
              </h1>
              <p className="text-xl max-w-3xl mx-auto" style={{color: '#6b7c5d'}}>
                {currentIssueData.volume}, {currentIssueData.issue} - {currentIssueData.date}
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="py-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              
              {/* Issue Cover and Info */}
              <div className="lg:col-span-1">
                <div className="sticky top-8">
                  <div className="rounded-lg shadow-lg overflow-hidden" style={{backgroundColor: '#faf9f7'}}>
                    <img 
                      src={currentIssueData.coverImage} 
                      alt="Current Issue Cover"
                      className="w-full h-96 object-cover"
                    />
                    <div className="p-6">
                      <h3 className="text-2xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                        {currentIssueData.volume}, {currentIssueData.issue}
                      </h3>
                      <p className="text-lg mb-4" style={{color: '#6b7c5d'}}>
                        Published: {currentIssueData.date}
                      </p>
                      <p className="text-sm mb-6" style={{color: '#6b7c5d'}}>
                        {currentIssueData.description}
                      </p>
                      
                      <div className="space-y-3">
                        <button 
                          className="w-full py-3 px-4 rounded-lg font-semibold transition-colors"
                          style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                          onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                          onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                        >
                          Download Full Issue (PDF)
                        </button>
                        <button 
                          className="w-full py-3 px-4 rounded-lg font-semibold transition-colors border"
                          style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                          onMouseOver={(e) => {
                            e.target.style.backgroundColor = '#6b7c5d';
                            e.target.style.color = '#f5f5f0';
                          }}
                          onMouseOut={(e) => {
                            e.target.style.backgroundColor = 'transparent';
                            e.target.style.color = '#6b7c5d';
                          }}
                        >
                          View Table of Contents
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Articles List */}
              <div className="lg:col-span-2">
                <div className="mb-8">
                  <h2 className="text-3xl font-bold mb-6" style={{color: '#4a5d3a'}}>
                    Articles in This Issue
                  </h2>
                  
                  <div className="space-y-8">
                    {articles.map((article) => (
                      <div 
                        key={article.id}
                        className="rounded-lg shadow-md p-6 transition-shadow hover:shadow-lg"
                        style={{backgroundColor: '#faf9f7'}}
                      >
                        <div className="flex justify-between items-start mb-4">
                          <span 
                            className="px-3 py-1 rounded-full text-xs font-semibold"
                            style={{backgroundColor: '#e8ede6', color: '#4a5d3a'}}
                          >
                            {article.type}
                          </span>
                          <span className="text-sm" style={{color: '#6b7c5d'}}>
                            Pages {article.pages}
                          </span>
                        </div>
                        
                        <h3 className="text-xl font-bold mb-3 hover:underline cursor-pointer" style={{color: '#4a5d3a'}}>
                          {article.title}
                        </h3>
                        
                        <div className="mb-4">
                          <p className="text-sm mb-2" style={{color: '#6b7c5d'}}>
                            <strong>Authors: <AUTHORS>
                          </p>
                          <p className="text-sm mb-2" style={{color: '#6b7c5d'}}>
                            <strong>DOI:</strong> {article.doi}
                          </p>
                        </div>
                        
                        <p className="text-sm mb-4" style={{color: '#6b7c5d'}}>
                          <strong>Abstract:</strong> {article.abstract}
                        </p>
                        
                        <div className="mb-4">
                          <p className="text-sm mb-2" style={{color: '#6b7c5d'}}>
                            <strong>Keywords:</strong> {article.keywords.join(', ')}
                          </p>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex space-x-4 text-sm" style={{color: '#6b7c5d'}}>
                            <span>👁️ {article.views} views</span>
                            <span>⬇️ {article.downloads} downloads</span>
                          </div>
                          <div className="flex space-x-3">
                            <button 
                              className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                              style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                              onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                              onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                            >
                              View Abstract
                            </button>
                            <button 
                              className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors border"
                              style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                              onMouseOver={(e) => {
                                e.target.style.backgroundColor = '#6b7c5d';
                                e.target.style.color = '#f5f5f0';
                              }}
                              onMouseOut={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.color = '#6b7c5d';
                              }}
                            >
                              Download PDF
                            </button>
                            <button 
                              className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors border"
                              style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                              onMouseOver={(e) => {
                                e.target.style.backgroundColor = '#6b7c5d';
                                e.target.style.color = '#f5f5f0';
                              }}
                              onMouseOut={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.color = '#6b7c5d';
                              }}
                            >
                              Cite Article
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  🔓
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Open Access
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  All articles in this issue are freely available under Creative Commons licensing.
                </p>
              </div>
              
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  🔍
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Peer Reviewed
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  All research articles undergo rigorous double-blind peer review process.
                </p>
              </div>
              
              <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                  📊
                </div>
                <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                  Indexed
                </h3>
                <p className="text-sm" style={{color: '#6b7c5d'}}>
                  Articles are indexed in major legal and academic databases worldwide.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default CurrentIssue;
