import { useState } from 'react';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

function BookPublished() {
  const [searchTerm, setSearchTerm] = useState('');

  const books = [
    {
      id: 1,
      title: "International Law: Principles and Practice",
      authors: "Prof. <PERSON>, Dr. <PERSON>",
      publisher: "Legal Publishing House",
      publicationDate: "2024",
      isbn: "978-0-123456-78-9",
      abstract: "A comprehensive guide to modern international law principles and their practical applications in contemporary legal scenarios...",
      keywords: ["International Law", "Legal Practice", "Global Jurisprudence"],
      pages: 450,
      price: "$89.99"
    },
    {
      id: 2,
      title: "Constitutional Law and Democracy",
      authors: "Dr. <PERSON>, Prof. <PERSON>",
      publisher: "Academic Legal Press",
      publicationDate: "2023",
      isbn: "978-0-987654-32-1",
      abstract: "An in-depth analysis of constitutional principles and their role in maintaining democratic institutions...",
      keywords: ["Constitutional Law", "Democracy", "Governance"],
      pages: 380,
      price: "$75.00"
    },
    // Add more books as needed
  ];

  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
    book.keywords.some(keyword =>
      keyword.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f5f5f0]">
        {/* Header Section */}
        <div className="bg-[#faf9f7] py-8 px-4">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold text-[#4a5d3a] mb-4">Published Books</h1>
            <p className="text-[#6b7c5d] mb-6">
              Explore our collection of published legal books and scholarly works
            </p>
            {/* Search Bar */}
            <input
              type="text"
              placeholder="Search books by title, author, or keyword..."
              className="w-full max-w-2xl px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-[#4a5d3a]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Books List */}
        <div className="max-w-7xl mx-auto py-8 px-4">
          <div className="space-y-6">
            {filteredBooks.map((book) => (
              <div
                key={book.id}
                className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              >
                <h2 className="text-xl font-semibold text-[#4a5d3a] mb-2">{book.title}</h2>
                <p className="text-sm text-gray-600 mb-2">{book.authors}</p>
                <p className="text-sm text-gray-500 mb-2">
                  Publisher: {book.publisher} | Published: {book.publicationDate}
                </p>
                <p className="text-sm text-gray-500 mb-2">ISBN: {book.isbn}</p>
                <p className="text-gray-700 mb-4">{book.abstract}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {book.keywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-[#e8ede6] text-[#4a5d3a] rounded-full text-sm"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">{book.pages} pages</span>
                  <div className="flex items-center gap-4">
                    <span className="text-lg font-semibold text-[#4a5d3a]">{book.price}</span>
                    <button className="px-4 py-2 bg-[#4a5d3a] text-white rounded-lg hover:bg-[#3a4d2a] transition-colors">
                      Purchase
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default BookPublished;
