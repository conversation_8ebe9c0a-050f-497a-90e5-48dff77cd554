import React from 'react';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';

function AdvisoryBoardNew() {
  const advisoryMembers = [
    {
      id: 1,
      name: "Prof. Dr. <PERSON>",
      specialization: "Constitutional Law, Human Rights Law",
      affiliation: "Department of Constitutional Law, Harvard Law School, Cambridge, MA, USA",
      email: "m.<PERSON><EMAIL>",
      reviewerId: "JLS-ADV-001"
    },
    {
      id: 2,
      name: "Dr. <PERSON>",
      specialization: "International Trade Law, WTO Law",
      affiliation: "Department of International Law, Stanford Law School, Stanford, CA, USA",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-002"
    },
    {
      id: 3,
      name: "Prof. <PERSON>",
      specialization: "Criminal Law, Legal Theory, Jurisprudence",
      affiliation: "Faculty of Law, Oxford University, Oxford, United Kingdom",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-003"
    },
    {
      id: 4,
      name: "Dr. <PERSON>",
      specialization: "European Union Law, International Human Rights",
      affiliation: "Department of Public Law, Universidad Complutense Madrid, Madrid, Spain",
      email: "m.<PERSON><PERSON>@ucm.es",
      reviewerId: "JLS-ADV-004"
    },
    {
      id: 5,
      name: "Prof. David Kim",
      specialization: "Corporate Law, Securities Regulation, Business Law",
      affiliation: "College of Law, Seoul National University, Seoul, South Korea",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-005"
    },
    {
      id: 6,
      name: "Dr. Emma Thompson",
      specialization: "Legal History, Common Law Development",
      affiliation: "Faculty of Law, University of Cambridge, Cambridge, United Kingdom",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-006"
    },
    {
      id: 7,
      name: "Prof. Ahmed Hassan",
      specialization: "Islamic Law, Comparative Legal Systems",
      affiliation: "Faculty of Law, Cairo University, Cairo, Egypt",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-007"
    },
    {
      id: 8,
      name: "Dr. Lisa Anderson",
      specialization: "Environmental Law, Climate Justice, Sustainability Law",
      affiliation: "Faculty of Law, University of Toronto, Toronto, ON, Canada",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-008"
    },
    {
      id: 9,
      name: "Prof. Roberto Silva",
      specialization: "Civil Law, Contract Law, Property Law",
      affiliation: "Faculdade de Direito, Universidade de São Paulo, São Paulo, Brazil",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-009"
    },
    {
      id: 10,
      name: "Dr. Priya Sharma",
      specialization: "Intellectual Property Law, Technology Law",
      affiliation: "Faculty of Law, National Law School of India University, Bangalore, India",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-010"
    },
    {
      id: 11,
      name: "Prof. Jean-Pierre Dubois",
      specialization: "Administrative Law, Public Law, French Legal System",
      affiliation: "Faculté de Droit, Université Paris 1 Panthéon-Sorbonne, Paris, France",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-011"
    },
    {
      id: 12,
      name: "Dr. Klaus Mueller",
      specialization: "Tax Law, Financial Regulation, European Tax Policy",
      affiliation: "Faculty of Law, Ludwig Maximilian University of Munich, Munich, Germany",
      email: "<EMAIL>",
      reviewerId: "JLS-ADV-012"
    }
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        
        {/* Header Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Advisory Board Members
              </h1>
              <p className="text-xl max-w-4xl mx-auto" style={{color: '#6b7c5d'}}>
                Our distinguished advisory board comprises internationally recognized legal scholars who provide strategic guidance, ensure academic excellence, and contribute to the advancement of legal research and scholarship.
              </p>
            </div>
          </div>
        </div>

        {/* Advisory Board Members List */}
        <div className="py-12">
          <div className="max-w-6xl mx-auto px-4">
            
            {/* Introduction */}
            <div className="mb-12 text-center">
              <div className="inline-block px-6 py-3 rounded-lg mb-6" style={{backgroundColor: '#e8ede6'}}>
                <span className="text-lg font-semibold" style={{color: '#4a5d3a'}}>
                  {advisoryMembers.length} Distinguished Members
                </span>
              </div>
              <p className="text-lg max-w-3xl mx-auto" style={{color: '#6b7c5d'}}>
                Each member brings unique expertise and international perspective to guide our journal's mission of advancing legal scholarship through rigorous peer-reviewed research.
              </p>
            </div>

            {/* Members List - Following journalijar.com structure */}
            <div className="space-y-6">
              {advisoryMembers.map((member, index) => (
                <div 
                  key={member.id}
                  className="rounded-lg shadow-sm p-6 transition-all hover:shadow-md"
                  style={{backgroundColor: '#faf9f7'}}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-2" style={{color: '#4a5d3a'}}>
                        {member.name}
                      </h3>
                      <div className="space-y-1 text-sm" style={{color: '#6b7c5d'}}>
                        <p>
                          <strong>Specialization:</strong> <em>{member.specialization}</em>
                        </p>
                        <p>
                          <strong>Affiliation:</strong> {member.affiliation}
                        </p>
                        <p>
                          <strong>Email:</strong> <em>{member.email}</em>
                        </p>
                        <p>
                          <strong>Reviewer ID:</strong> {member.reviewerId}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Information Section */}
            <div className="mt-16">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                    🎯
                  </div>
                  <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                    Strategic Guidance
                  </h3>
                  <p className="text-sm" style={{color: '#6b7c5d'}}>
                    Providing strategic direction for journal development and maintaining the highest academic standards in legal scholarship.
                  </p>
                </div>
                
                <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                    ⚖️
                  </div>
                  <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                    Quality Assurance
                  </h3>
                  <p className="text-sm" style={{color: '#6b7c5d'}}>
                    Ensuring rigorous peer review processes and maintaining excellence in legal research publication standards.
                  </p>
                </div>
                
                <div className="text-center p-6 rounded-lg" style={{backgroundColor: '#faf9f7'}}>
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl" style={{backgroundColor: '#e8ede6'}}>
                    🌍
                  </div>
                  <h3 className="text-lg font-semibold mb-2" style={{color: '#4a5d3a'}}>
                    Global Perspective
                  </h3>
                  <p className="text-sm" style={{color: '#6b7c5d'}}>
                    Bringing international expertise and diverse legal perspectives from leading institutions worldwide.
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-16 text-center">
              <div className="rounded-lg p-8" style={{backgroundColor: '#faf9f7'}}>
                <h3 className="text-2xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                  Join Our Academic Community
                </h3>
                <p className="text-lg mb-6 max-w-2xl mx-auto" style={{color: '#6b7c5d'}}>
                  Interested in contributing to legal scholarship? Learn about opportunities to join our review panel or submit your research for publication.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button 
                    className="px-8 py-3 rounded-lg font-semibold transition-colors"
                    style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                  >
                    Join as Reviewer
                  </button>
                  <button 
                    className="px-8 py-3 rounded-lg font-semibold transition-colors border"
                    style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                    onMouseOver={(e) => {
                      e.target.style.backgroundColor = '#6b7c5d';
                      e.target.style.color = '#f5f5f0';
                    }}
                    onMouseOut={(e) => {
                      e.target.style.backgroundColor = 'transparent';
                      e.target.style.color = '#6b7c5d';
                    }}
                  >
                    Submit Manuscript
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default AdvisoryBoardNew;
