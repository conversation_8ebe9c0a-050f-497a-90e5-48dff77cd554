import { useState } from 'react';
import Navbar from '../Components/Navbar';
import Footer from '../Components/Footer';

function HomeNew() {
  const [searchTerm, setSearchTerm] = useState('');

  const recentArticles = [
    {
      id: 1,
      category: "Criminal Law",
      volume: "Vol. 14, Issue 1",
      title: "Reforming Criminal Justice: A Comparative Study",
      authors: "<PERSON><PERSON>, <PERSON>",
      date: "March 2025",
      abstract: "This study explores international models of criminal justice reform, comparing the legal frameworks and social implications of changes across five countries.",
      views: 1245,
      downloads: 980
    },
    {
      id: 2,
      category: "Constitutional Law",
      volume: "Vol. 14, Issue 1",
      title: "The Role of Judicial Review in Democratic Societies",
      authors: "<PERSON><PERSON>, <PERSON>",
      date: "March 2025",
      abstract: "An analysis of how constitutional courts maintain the balance of power in democratic systems, focusing on landmark cases from India, USA, and Germany.",
      views: 1082,
      downloads: 870
    },
    {
      id: 3,
      category: "Environmental Law",
      volume: "Vol. 14, Issue 1",
      title: "Climate Change Litigation: Trends and Challenges",
      authors: "<PERSON>, <PERSON><PERSON>",
      date: "March 2025",
      abstract: "This paper evaluates the effectiveness of climate change litigation in driving policy change and environmental accountability at the national level.",
      views: 965,
      downloads: 790
    }
  ];

  const announcements = [
    {
      id: 1,
      title: "Call for Papers: Vol. 15, Issue 1",
      type: "Call for Papers",
      date: "June 1, 2025",
      urgent: true
    },
    {
      id: 2,
      title: "Extended Submission Deadline for Vol. 14, Issue 4",
      type: "Deadline Extension",
      date: "May 25, 2025",
      urgent: false
    }
  ];

  const quickStats = [
    { label: "Total Articles", value: "1,250+", icon: "📄" },
    { label: "Citations", value: "15,000+", icon: "📊" },
    { label: "Countries", value: "45+", icon: "🌍" },
    { label: "Impact Factor", value: "2.45", icon: "⭐" }
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen bg-[#f5f5f0]">
        {/* Hero Section */}
        <div className="py-12 bg-[#faf9f7] px-4">
          <div className="max-w-7xl mx-auto text-center mb-8">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-[#4a5d3a]">
              Welcome to Journal of Legal Studies
            </h1>
            <p className="text-base sm:text-lg md:text-xl mb-8 mx-auto max-w-3xl text-[#6b7c5d]">
              A premier international journal dedicated to advancing legal scholarship through rigorous peer-reviewed research and innovative academic discourse
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <div className="flex flex-col sm:flex-row rounded-lg overflow-hidden shadow-lg">
                <input
                  type="text"
                  placeholder="Search articles, authors, keywords..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 px-4 py-3 sm:px-6 sm:py-4 text-lg focus:outline-none bg-white text-[#4a5d3a]"
                />
                <button 
                  className="px-6 py-3 sm:px-8 sm:py-4 text-lg font-semibold bg-[#6b7c5d] text-[#f5f5f0] hover:bg-[#8a9a7b]"
                >
                  Search
                </button>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 md:gap-6 max-w-4xl mx-auto">
              {quickStats.map((stat, index) => (
                <div key={index} className="text-center p-4 rounded-lg bg-[#e8ede6]">
                  <div className="text-2xl mb-2">{stat.icon}</div>
                  <div className="text-xl md:text-2xl font-bold text-[#4a5d3a]">{stat.value}</div>
                  <div className="text-sm text-[#6b7c5d]">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="py-12 px-4">
          <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Articles */}
            <div className="lg:col-span-2">
              <h2 className="text-2xl md:text-3xl font-bold mb-6 text-[#4a5d3a]">Recent Publications</h2>
              <div className="space-y-6">
                {recentArticles.map(article => (
                  <div key={article.id} className="rounded-lg shadow-md p-6 bg-[#faf9f7] hover:shadow-lg transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <span className="px-3 py-1 rounded-full text-xs font-semibold bg-[#e8ede6] text-[#4a5d3a]">
                        {article.category}
                      </span>
                      <span className="text-sm text-[#6b7c5d]">{article.volume}</span>
                    </div>
                    <h3 className="text-lg md:text-xl font-bold mb-3 text-[#4a5d3a] hover:underline cursor-pointer">
                      {article.title}
                    </h3>
                    <p className="text-sm text-[#6b7c5d] mb-1"><strong>Authors: <AUTHORS>
                    <p className="text-sm text-[#6b7c5d] mb-2"><strong>Published:</strong> {article.date}</p>
                    <p className="text-sm text-[#6b7c5d] mb-4">{article.abstract}</p>
                    <div className="flex justify-between items-center flex-wrap gap-2">
                      <div className="flex space-x-4 text-sm text-[#6b7c5d]">
                        <span>👁️ {article.views} views</span>
                        <span>⬇️ {article.downloads} downloads</span>
                      </div>
                      <div className="flex space-x-3">
                        <button className="px-4 py-2 rounded-lg text-sm font-semibold bg-[#6b7c5d] text-[#f5f5f0] hover:bg-[#8a9a7b]">
                          Read More
                        </button>
                        <button className="px-4 py-2 rounded-lg text-sm font-semibold border border-[#6b7c5d] text-[#6b7c5d] hover:bg-[#6b7c5d] hover:text-[#f5f5f0]">
                          Download PDF
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="text-center mt-8">
                <button className="px-8 py-3 rounded-lg text-lg font-semibold bg-[#6b7c5d] text-[#f5f5f0] hover:bg-[#8a9a7b]">
                  View All Articles
                </button>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              <div className="bg-[#faf9f7] p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4 text-[#4a5d3a]">Quick Links</h3>
                <ul className="space-y-3 text-sm text-[#6b7c5d]">
                  <li><a href="/current-issue" className="hover:text-[#4a5d3a]">📖 Current Issue</a></li>
                  <li><a href="/submit-manuscript" className="hover:text-[#4a5d3a]">✍️ Submit Manuscript</a></li>
                  <li><a href="/author-guidelines" className="hover:text-[#4a5d3a]">📋 Author Guidelines</a></li>
                  <li><a href="/peer-review" className="hover:text-[#4a5d3a]">🔍 Peer Review Process</a></li>
                  <li><a href="/indexing" className="hover:text-[#4a5d3a]">📊 Indexing & Abstracting</a></li>
                </ul>
              </div>

              <div className="bg-[#faf9f7] p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4 text-[#4a5d3a]">Announcements</h3>
                <div className="space-y-4 text-sm">
                  {announcements.map((a) => (
                    <div key={a.id} className={`border-l-4 pl-4 ${a.urgent ? 'border-yellow-500' : 'border-[#6b7c5d]'}`}>
                      <div className="flex justify-between mb-1">
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${a.urgent ? 'bg-yellow-100 text-yellow-600' : 'bg-[#e8ede6] text-[#4a5d3a]'}`}>{a.type}</span>
                        {a.urgent && <span className="text-xs text-yellow-600">🔥 Urgent</span>}
                      </div>
                      <h4 className="font-semibold text-sm text-[#4a5d3a] mb-1">{a.title}</h4>
                      <p className="text-xs text-[#6b7c5d]">{a.date}</p>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <a href="/announcements" className="text-sm font-semibold text-[#6b7c5d] hover:text-[#4a5d3a]">
                    View All Announcements →
                  </a>
                </div>
              </div>

              <div className="bg-[#faf9f7] p-6 rounded-lg text-sm text-[#6b7c5d]">
                <h3 className="text-xl font-bold mb-4 text-[#4a5d3a]">Journal Information</h3>
                <p><strong>ISSN:</strong> 2456-7890 (Online)</p>
                <p><strong>Frequency:</strong> Quarterly</p>
                <p><strong>Language:</strong> English</p>
                <p><strong>Publisher:</strong> Legal Studies Press</p>
                <p><strong>Impact Factor:</strong> 2.45 (2024)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default HomeNew;
