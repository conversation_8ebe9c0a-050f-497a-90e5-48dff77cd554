import { useState } from 'react';
import Navbar from '../Components/Navbar';
import Footer from '../Components/Footer';

function HomeNew() {
  const [searchTerm, setSearchTerm] = useState('');

  const recentArticles = [
    {
      id: 1,
      title: "Constitutional Interpretation in the Digital Age: Privacy Rights and Technological Surveillance",
      authors: "Dr. <PERSON>, Prof. <PERSON>",
      date: "December 15, 2024",
      volume: "Vol. 15, Issue 2",
      category: "Constitutional Law",
      abstract: "This article examines the evolving landscape of constitutional privacy rights in response to advancing surveillance technologies...",
      downloads: 245,
      views: 1250
    },
    {
      id: 2,
      title: "International Trade Law and Environmental Protection: Reconciling Economic and Ecological Interests",
      authors: "Prof<PERSON> <PERSON>, Dr. <PERSON>",
      date: "December 10, 2024",
      volume: "Vol. 15, Issue 2",
      category: "International Law",
      abstract: "This study analyzes the tension between international trade agreements and environmental protection measures...",
      downloads: 189,
      views: 890
    },
    {
      id: 3,
      title: "Artificial Intelligence and Legal Decision-Making: Ethical Considerations for Judicial Systems",
      authors: "<PERSON><PERSON> <PERSON>, Prof. <PERSON>",
      date: "December 5, 2024",
      volume: "Vol. 15, Issue 2",
      category: "Legal Technology",
      abstract: "As AI systems become increasingly integrated into legal processes, this article explores the ethical implications...",
      downloads: 312,
      views: 1450
    },
    {
      id: 4,
      title: "Corporate Social Responsibility and Legal Accountability: A Comparative Analysis",
      authors: "Prof. Emma Wilson",
      date: "November 28, 2024",
      volume: "Vol. 15, Issue 1",
      category: "Corporate Law",
      abstract: "This comparative study examines how different legal systems approach corporate social responsibility...",
      downloads: 156,
      views: 720
    }
  ];

  const announcements = [
    {
      id: 1,
      title: "Call for Papers: Special Issue on AI and Law",
      date: "December 20, 2024",
      type: "Call for Papers",
      urgent: true
    },
    {
      id: 2,
      title: "New Editorial Board Members Appointed",
      date: "December 15, 2024",
      type: "Announcement"
    },
    {
      id: 3,
      title: "Journal Impact Factor Update",
      date: "December 10, 2024",
      type: "News"
    },
    {
      id: 4,
      title: "Submission Guidelines Updated",
      date: "December 5, 2024",
      type: "Guidelines"
    }
  ];

  const quickStats = [
    { label: "Total Articles", value: "1,250+", icon: "📄" },
    { label: "Citations", value: "15,000+", icon: "📊" },
    { label: "Countries", value: "45+", icon: "🌍" },
    { label: "Impact Factor", value: "2.45", icon: "⭐" }
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        
        {/* Hero Section */}
        <div className="py-12" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Welcome to Journal of Legal Studies
              </h1>
              <p className="text-xl mb-8 max-w-3xl mx-auto" style={{color: '#6b7c5d'}}>
                A premier international journal dedicated to advancing legal scholarship through rigorous peer-reviewed research and innovative academic discourse
              </p>
              
              {/* Search Bar */}
              <div className="max-w-2xl mx-auto mb-8">
                <div className="flex rounded-lg overflow-hidden shadow-lg">
                  <input
                    type="text"
                    placeholder="Search articles, authors, keywords..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1 px-6 py-4 text-lg focus:outline-none"
                    style={{backgroundColor: '#ffffff', color: '#4a5d3a'}}
                  />
                  <button 
                    className="px-8 py-4 text-lg font-semibold transition-colors"
                    style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                    onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                    onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                  >
                    Search
                  </button>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                {quickStats.map((stat, index) => (
                  <div key={index} className="text-center p-4 rounded-lg" style={{backgroundColor: '#e8ede6'}}>
                    <div className="text-2xl mb-2">{stat.icon}</div>
                    <div className="text-2xl font-bold" style={{color: '#4a5d3a'}}>{stat.value}</div>
                    <div className="text-sm" style={{color: '#6b7c5d'}}>{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="py-12">
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              
              {/* Main Content - Recent Articles */}
              <div className="lg:col-span-2">
                <div className="mb-8">
                  <h2 className="text-3xl font-bold mb-6" style={{color: '#4a5d3a'}}>
                    Recent Publications
                  </h2>
                  
                  <div className="space-y-6">
                    {recentArticles.map((article) => (
                      <div 
                        key={article.id} 
                        className="rounded-lg shadow-md p-6 transition-shadow hover:shadow-lg"
                        style={{backgroundColor: '#faf9f7'}}
                      >
                        <div className="flex justify-between items-start mb-4">
                          <span 
                            className="px-3 py-1 rounded-full text-xs font-semibold"
                            style={{backgroundColor: '#e8ede6', color: '#4a5d3a'}}
                          >
                            {article.category}
                          </span>
                          <span className="text-sm" style={{color: '#6b7c5d'}}>
                            {article.volume}
                          </span>
                        </div>
                        
                        <h3 className="text-xl font-bold mb-3 hover:underline cursor-pointer" style={{color: '#4a5d3a'}}>
                          {article.title}
                        </h3>
                        
                        <div className="mb-3">
                          <p className="text-sm" style={{color: '#6b7c5d'}}>
                            <strong>Authors: <AUTHORS>
                          </p>
                          <p className="text-sm" style={{color: '#6b7c5d'}}>
                            <strong>Published:</strong> {article.date}
                          </p>
                        </div>
                        
                        <p className="text-sm mb-4" style={{color: '#6b7c5d'}}>
                          {article.abstract}
                        </p>
                        
                        <div className="flex justify-between items-center">
                          <div className="flex space-x-4 text-sm" style={{color: '#6b7c5d'}}>
                            <span>👁️ {article.views} views</span>
                            <span>⬇️ {article.downloads} downloads</span>
                          </div>
                          <div className="flex space-x-3">
                            <button 
                              className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors"
                              style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                              onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                              onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                            >
                              Read More
                            </button>
                            <button 
                              className="px-4 py-2 rounded-lg text-sm font-semibold transition-colors border"
                              style={{borderColor: '#6b7c5d', color: '#6b7c5d', backgroundColor: 'transparent'}}
                              onMouseOver={(e) => {
                                e.target.style.backgroundColor = '#6b7c5d';
                                e.target.style.color = '#f5f5f0';
                              }}
                              onMouseOut={(e) => {
                                e.target.style.backgroundColor = 'transparent';
                                e.target.style.color = '#6b7c5d';
                              }}
                            >
                              Download PDF
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* View All Articles Button */}
                  <div className="text-center mt-8">
                    <button 
                      className="px-8 py-3 rounded-lg text-lg font-semibold transition-colors"
                      style={{backgroundColor: '#6b7c5d', color: '#f5f5f0'}}
                      onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                      onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                    >
                      View All Articles
                    </button>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-8">
                  
                  {/* Quick Links */}
                  <div className="rounded-lg p-6" style={{backgroundColor: '#faf9f7'}}>
                    <h3 className="text-xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                      Quick Links
                    </h3>
                    <ul className="space-y-3">
                      <li>
                        <a href="/current-issue" className="flex items-center text-sm transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                          📖 Current Issue
                        </a>
                      </li>
                      <li>
                        <a href="/submit-manuscript" className="flex items-center text-sm transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                          📝 Submit Manuscript
                        </a>
                      </li>
                      <li>
                        <a href="/author-guidelines" className="flex items-center text-sm transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                          📋 Author Guidelines
                        </a>
                      </li>
                      <li>
                        <a href="/peer-review" className="flex items-center text-sm transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                          🔍 Peer Review Process
                        </a>
                      </li>
                      <li>
                        <a href="/indexing" className="flex items-center text-sm transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                          📊 Indexing & Abstracting
                        </a>
                      </li>
                    </ul>
                  </div>

                  {/* Announcements */}
                  <div className="rounded-lg p-6" style={{backgroundColor: '#faf9f7'}}>
                    <h3 className="text-xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                      Announcements
                    </h3>
                    <div className="space-y-4">
                      {announcements.map((announcement) => (
                        <div key={announcement.id} className="border-l-4 pl-4" style={{borderColor: announcement.urgent ? '#d97706' : '#6b7c5d'}}>
                          <div className="flex items-center justify-between mb-1">
                            <span 
                              className="px-2 py-1 rounded text-xs font-semibold"
                              style={{
                                backgroundColor: announcement.urgent ? '#fef3c7' : '#e8ede6',
                                color: announcement.urgent ? '#d97706' : '#4a5d3a'
                              }}
                            >
                              {announcement.type}
                            </span>
                            {announcement.urgent && <span className="text-xs" style={{color: '#d97706'}}>🔥 Urgent</span>}
                          </div>
                          <h4 className="font-semibold text-sm mb-1" style={{color: '#4a5d3a'}}>
                            {announcement.title}
                          </h4>
                          <p className="text-xs" style={{color: '#6b7c5d'}}>
                            {announcement.date}
                          </p>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4">
                      <a href="/announcements" className="text-sm font-semibold transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.color = '#4a5d3a'} onMouseOut={(e) => e.target.style.color = '#6b7c5d'}>
                        View All Announcements →
                      </a>
                    </div>
                  </div>

                  {/* Journal Info */}
                  <div className="rounded-lg p-6" style={{backgroundColor: '#faf9f7'}}>
                    <h3 className="text-xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                      Journal Information
                    </h3>
                    <div className="space-y-3 text-sm" style={{color: '#6b7c5d'}}>
                      <div>
                        <strong>ISSN:</strong> 2456-7890 (Online)
                      </div>
                      <div>
                        <strong>Frequency:</strong> Quarterly
                      </div>
                      <div>
                        <strong>Language:</strong> English
                      </div>
                      <div>
                        <strong>Publisher:</strong> Legal Studies Press
                      </div>
                      <div>
                        <strong>Impact Factor:</strong> 2.45 (2024)
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default HomeNew;
