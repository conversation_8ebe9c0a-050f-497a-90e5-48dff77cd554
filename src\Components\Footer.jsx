
import { useState } from 'react';
import { Link } from 'react-router-dom';

function Footer() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (email.trim()) {
      // Here you would typically send the email to your backend
      console.log('Subscribing email:', email);
      setIsSubscribed(true);
      setEmail('');

      // Reset the success message after 3 seconds
      setTimeout(() => {
        setIsSubscribed(false);
      }, 3000);
    }
  };
  return (
    <footer className="text-white py-12" style={{backgroundColor: '#4a5d3a'}}>
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

          {/* About Section */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold" style={{backgroundColor: '#f5f5f0', color: '#4a5d3a'}}>
                JLS
              </div>
              <h3 className="text-lg font-bold" style={{color: '#f5f5f0'}}>Journal of Legal Studies</h3>
            </div>
            <p className="text-sm mb-4" style={{color: '#d1dcc9'}}>
              A premier international journal dedicated to advancing legal scholarship through rigorous peer-reviewed research and innovative academic discourse.
            </p>
            <div className="text-sm space-y-1" style={{color: '#d1dcc9'}}>
              <p><strong>ISSN:</strong> 2456-7890</p>
              <p><strong>Impact Factor:</strong> 2.45</p>
              <p><strong>Frequency:</strong> Quarterly</p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4" style={{color: '#f5f5f0'}}>Quick Links</h3>
            <ul className="text-sm space-y-3">
              <li>
                <Link to="/current-issue" className="flex items-center transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                  📖 Current Issue
                </Link>
              </li>
              <li>
                <Link to="/manuscript-submission" className="flex items-center transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                  📝 Submit Manuscript
                </Link>
              </li>
              <li>
                <Link to="/EditorialOrganization/EditorialBoard" className="flex items-center transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                  👥 Editorial Board
                </Link>
              </li>
              <li>
                <Link to="/author-guidelines" className="flex items-center transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                  📋 Author Guidelines
                </Link>
              </li>
              <li>
                <Link to="/ethics-policy" className="flex items-center transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                  ⚖️ Publication Ethics
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-bold mb-4" style={{color: '#f5f5f0'}}>Contact Information</h3>
            <ul className="text-sm space-y-3" style={{color: '#d1dcc9'}}>
              <li className="flex items-center">
                <span className="mr-2">📧</span>
                <a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a>
              </li>
              <li className="flex items-center">
                <span className="mr-2">📞</span>
                <a href="tel:+15551234567" className="hover:underline">+****************</a>
              </li>
              <li className="flex items-start">
                <span className="mr-2 mt-0.5">📍</span>
                <span>123 Legal Avenue<br />Jurisprudence City, JC 12345<br />United States</span>
              </li>
              <li className="flex items-center">
                <span className="mr-2">🌐</span>
                <a href="https://www.jls.org" className="hover:underline">www.jls.org</a>
              </li>
            </ul>
          </div>

          {/* Newsletter Subscription */}
          <div>
            <h3 className="text-lg font-bold mb-4" style={{color: '#f5f5f0'}}>Stay Updated</h3>
            <p className="text-sm mb-4" style={{color: '#d1dcc9'}}>
              Subscribe to receive notifications about new issues, calls for papers, and journal updates.
            </p>
            {isSubscribed ? (
              <div className="text-white px-4 py-3 rounded-lg text-sm flex items-center" style={{backgroundColor: '#8a9a7b'}}>
                <span className="mr-2">✓</span>
                Successfully subscribed!
              </div>
            ) : (
              <form onSubmit={handleSubscribe} className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 text-sm"
                  style={{color: '#4a5d3a', backgroundColor: '#f5f5f0', '--tw-ring-color': '#6b7c5d'}}
                  required
                />
                <button
                  type="submit"
                  className="w-full py-3 rounded-lg text-sm font-semibold transition-colors duration-200 focus:outline-none focus:ring-2"
                  style={{backgroundColor: '#6b7c5d', color: '#f5f5f0', '--tw-ring-color': '#6b7c5d'}}
                  onMouseOver={(e) => e.target.style.backgroundColor = '#8a9a7b'}
                  onMouseOut={(e) => e.target.style.backgroundColor = '#6b7c5d'}
                >
                  Subscribe to Newsletter
                </button>
              </form>
            )}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t" style={{borderColor: '#6b7c5d'}}>
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm" style={{color: '#d1dcc9'}}>
              <p>© 2024 Journal of Legal Studies. All rights reserved.</p>
            </div>
            <div className="flex space-x-6 text-sm">
              <Link to="/privacy-policy" className="transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                Privacy Policy
              </Link>
              <Link to="/terms-of-use" className="transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                Terms of Use
              </Link>
              <Link to="/disclaimer" className="transition-colors" style={{color: '#d1dcc9'}} onMouseOver={(e) => e.target.style.color = '#f5f5f0'} onMouseOut={(e) => e.target.style.color = '#d1dcc9'}>
                Disclaimer
              </Link>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-6 pt-6 border-t text-xs text-center" style={{borderColor: '#6b7c5d', color: '#d1dcc9'}}>
            <p>
              <strong>Disclaimer:</strong> The views and opinions expressed in the articles published in this journal are those of the individual authors and do not necessarily reflect the official policy or position of the Journal of Legal Studies, its editorial board, or the publisher. The journal is committed to maintaining the highest standards of academic integrity and ethical publishing practices.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;