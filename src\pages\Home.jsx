
import Navbar from '../Components/Navbar';
import Footer from '../Components/Footer';

function Home() {
  return (
    <>
      <Navbar />
      <div className="min-h-screen" style={{backgroundColor: '#f5f5f0'}}>
        {/* Hero Section */}
        <div className="text-white py-20" style={{background: 'linear-gradient(135deg, #8a9a7b 0%, #6b7c5d 100%)'}}>
          <div className="max-w-7xl mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Journal of Legal Studies
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Advancing legal scholarship through rigorous peer-reviewed research and innovative academic discourse
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white px-8 py-3 rounded-lg font-semibold transition-colors" style={{color: '#6b7c5d'}} onMouseOver={(e) => e.target.style.backgroundColor = '#f5f5f0'} onMouseOut={(e) => e.target.style.backgroundColor = 'white'}>
                Submit Manuscript
              </button>
              <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white transition-colors" style={{'--hover-color': '#6b7c5d'}} onMouseOver={(e) => {e.target.style.backgroundColor = 'white'; e.target.style.color = '#6b7c5d'}} onMouseOut={(e) => {e.target.style.backgroundColor = 'transparent'; e.target.style.color = 'white'}}>
                Browse Issues
              </button>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-16" style={{backgroundColor: '#faf9f7'}}>
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{color: '#4a5d3a'}}>
                Why Choose Our Journal?
              </h2>
              <p className="text-lg max-w-2xl mx-auto" style={{color: '#6b7c5d'}}>
                We are committed to publishing high-quality legal research that advances the field and serves the global academic community
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="p-6 rounded-lg" style={{backgroundColor: '#e8ede6', borderColor: '#d1dcc9', border: '1px solid'}}>
                <h3 className="text-xl font-semibold mb-3" style={{color: '#4a5d3a'}}>
                  Peer Review Process
                </h3>
                <p style={{color: '#6b7c5d'}}>
                  Rigorous double-blind peer review ensuring high-quality publications
                </p>
              </div>
              <div className="p-6 rounded-lg" style={{backgroundColor: '#e8ede6', borderColor: '#d1dcc9', border: '1px solid'}}>
                <h3 className="text-xl font-semibold mb-3" style={{color: '#4a5d3a'}}>
                  Fast Publication
                </h3>
                <p style={{color: '#6b7c5d'}}>
                  Rapid review and publication process without compromising quality
                </p>
              </div>
              <div className="p-6 rounded-lg" style={{backgroundColor: '#e8ede6', borderColor: '#d1dcc9', border: '1px solid'}}>
                <h3 className="text-xl font-semibold mb-3" style={{color: '#4a5d3a'}}>
                  Global Reach
                </h3>
                <p style={{color: '#6b7c5d'}}>
                  Wide international readership and visibility for your research
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default Home;