import { useState } from 'react';
import Navbar from '../../../Components/Navbar';
 import Footer from '../../../Components/Footer';
function PrePrint() {
    const [searchTerm, setSearchTerm] = useState('');
    const preprints = [
        {
            id: 1,
            title: "The Evolution of Digital Rights in Cyberlaw", authors: "Dr. <PERSON>, Prof. <PERSON>",
            submissionDate: "November 15, 2024", abstract: "This paper explores the development of digital rights and cyber legislation across different jurisdictions...",
            status: "Under Review", keywords: ["Cyberlaw", "Digital Rights", "Internet Governance"]
        }, {
            id: 2, title: "Environmental Law and Climate Change Litigation",
            authors: "Prof. <PERSON>, Dr. <PERSON>", submissionDate: "November 10, 2024",
            abstract: "An analysis of recent climate change litigation and its impact on environmental law development...", status: "Under Review",
            keywords: ["Environmental Law", "Climate Change", "Litigation"]
        }];
    return (<>
        <Navbar />      <div className="min-h-screen bg-[#f5f5f0]">
            {/* Header Section */}        <div className="bg-[#faf9f7] py-8 px-4">
                <div className="max-w-7xl mx-auto">            <h1 className="text-3xl font-bold text-[#4a5d3a] mb-4">Pre-prints</h1>
                    <p className="text-[#6b7c5d] mb-6">              Access the latest legal research papers currently under review
                    </p>
                    {/* Search Bar */}            <input
                        type="text" placeholder="Search pre-prints..."
                        className="w-full max-w-2xl px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-[#4a5d3a]"
                        value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                </div>        </div>
            {/* Pre-prints List */}
            <div className="max-w-7xl mx-auto py-8 px-4">          <div className="space-y-6">
                {preprints.map(preprint => (<div key={preprint.id} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <h2 className="text-xl font-semibold text-[#4a5d3a] mb-2">{preprint.title}</h2>                <p className="text-sm text-gray-600 mb-2">{preprint.authors}</p>
                    <p className="text-sm text-gray-500 mb-4">Submitted: {preprint.submissionDate}</p>                <p className="text-gray-700 mb-4">{preprint.abstract}</p>
                    <div className="flex flex-wrap gap-2 mb-4">                  {preprint.keywords.map((keyword, index) => (
                        <span key={index} className="px-3 py-1 bg-[#e8ede6] text-[#4a5d3a] rounded-full text-sm">                      {keyword}
                        </span>))}
                    </div>                <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-orange-600">{preprint.status}</span>                  <button className="px-4 py-2 bg-[#4a5d3a] text-white rounded-lg hover:bg-[#3a4d2a] transition-colors">
                            View Full Paper                  </button>
                    </div>              </div>
                ))}          </div>
            </div>
        </div>      <Footer />
    </>
    )
}

export default PrePrint;
;
